/**
 * INPUT MANAGER
 * 
 * Handles keyboard, mouse, and touch input for both desktop and mobile.
 * Provides a unified interface for all input types.
 */

class InputManager {
    constructor() {
        // Keyboard state
        this.keys = {};
        this.keysPressed = {};
        this.keysReleased = {};
        
        // Mouse state
        this.mouse = {
            x: 0,
            y: 0,
            worldX: 0,
            worldY: 0,
            leftButton: false,
            rightButton: false,
            leftPressed: false,
            rightPressed: false,
            leftReleased: false,
            rightReleased: false
        };
        
        // Touch state for mobile
        this.touches = [];
        this.virtualJoystick = {
            active: false,
            centerX: 0,
            centerY: 0,
            currentX: 0,
            currentY: 0,
            deltaX: 0,
            deltaY: 0
        };
        
        // Movement input (normalized -1 to 1)
        this.movement = {
            x: 0,
            y: 0
        };
        
        // Action states
        this.actions = {
            attack: false,
            interact: false,
            inventory: false,
            pause: false
        };
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.onKeyDown(e));
        document.addEventListener('keyup', (e) => this.onKeyUp(e));
        
        // Mouse events
        document.addEventListener('mousemove', (e) => this.onMouseMove(e));
        document.addEventListener('mousedown', (e) => this.onMouseDown(e));
        document.addEventListener('mouseup', (e) => this.onMouseUp(e));
        document.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Touch events for mobile
        document.addEventListener('touchstart', (e) => this.onTouchStart(e));
        document.addEventListener('touchmove', (e) => this.onTouchMove(e));
        document.addEventListener('touchend', (e) => this.onTouchEnd(e));
        
        // Prevent default touch behaviors
        document.addEventListener('touchstart', (e) => e.preventDefault(), { passive: false });
        document.addEventListener('touchmove', (e) => e.preventDefault(), { passive: false });

        // Mobile attack button
        const attackBtn = document.getElementById('attackBtn');
        if (attackBtn) {
            attackBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.actions.attack = true;
            });

            attackBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
            });
        }
    }
    
    onKeyDown(event) {
        const code = event.code;
        
        if (!this.keys[code]) {
            this.keysPressed[code] = true;
        }
        this.keys[code] = true;
        
        // Prevent default for game keys
        if (this.isGameKey(code)) {
            event.preventDefault();
        }
    }
    
    onKeyUp(event) {
        const code = event.code;
        this.keys[code] = false;
        this.keysReleased[code] = true;
        
        if (this.isGameKey(code)) {
            event.preventDefault();
        }
    }
    
    onMouseMove(event) {
        const canvas = document.getElementById('gameCanvas');
        const rect = canvas.getBoundingClientRect();
        
        this.mouse.x = (event.clientX - rect.left) * (canvas.width / rect.width);
        this.mouse.y = (event.clientY - rect.top) * (canvas.height / rect.height);
        
        // Convert to world coordinates (will be updated by camera)
        this.mouse.worldX = this.mouse.x;
        this.mouse.worldY = this.mouse.y;
    }
    
    onMouseDown(event) {
        if (event.button === 0) { // Left button
            this.mouse.leftButton = true;
            this.mouse.leftPressed = true;
        } else if (event.button === 2) { // Right button
            this.mouse.rightButton = true;
            this.mouse.rightPressed = true;
        }
        event.preventDefault();
    }
    
    onMouseUp(event) {
        if (event.button === 0) { // Left button
            this.mouse.leftButton = false;
            this.mouse.leftReleased = true;
        } else if (event.button === 2) { // Right button
            this.mouse.rightButton = false;
            this.mouse.rightReleased = true;
        }
        event.preventDefault();
    }
    
    onTouchStart(event) {
        for (let i = 0; i < event.changedTouches.length; i++) {
            const touch = event.changedTouches[i];
            const canvas = document.getElementById('gameCanvas');
            const rect = canvas.getBoundingClientRect();
            
            const touchX = (touch.clientX - rect.left) * (canvas.width / rect.width);
            const touchY = (touch.clientY - rect.top) * (canvas.height / rect.height);
            
            // Check if touch is in virtual joystick area (left side of screen)
            if (touchX < canvas.width / 2) {
                this.virtualJoystick.active = true;
                this.virtualJoystick.centerX = touchX;
                this.virtualJoystick.centerY = touchY;
                this.virtualJoystick.currentX = touchX;
                this.virtualJoystick.currentY = touchY;
            } else {
                // Right side touch is attack
                this.actions.attack = true;
            }
            
            this.touches.push({
                id: touch.identifier,
                x: touchX,
                y: touchY,
                startX: touchX,
                startY: touchY
            });
        }
    }
    
    onTouchMove(event) {
        for (let i = 0; i < event.changedTouches.length; i++) {
            const touch = event.changedTouches[i];
            const canvas = document.getElementById('gameCanvas');
            const rect = canvas.getBoundingClientRect();
            
            const touchX = (touch.clientX - rect.left) * (canvas.width / rect.width);
            const touchY = (touch.clientY - rect.top) * (canvas.height / rect.height);
            
            // Update existing touch
            const existingTouch = this.touches.find(t => t.id === touch.identifier);
            if (existingTouch) {
                existingTouch.x = touchX;
                existingTouch.y = touchY;
                
                // Update virtual joystick if this is the movement touch
                if (this.virtualJoystick.active && touchX < canvas.width / 2) {
                    this.virtualJoystick.currentX = touchX;
                    this.virtualJoystick.currentY = touchY;
                }
            }
        }
    }
    
    onTouchEnd(event) {
        for (let i = 0; i < event.changedTouches.length; i++) {
            const touch = event.changedTouches[i];
            
            // Remove touch from array
            this.touches = this.touches.filter(t => t.id !== touch.identifier);
            
            // If this was the movement touch, deactivate virtual joystick
            if (this.touches.length === 0) {
                this.virtualJoystick.active = false;
                this.virtualJoystick.deltaX = 0;
                this.virtualJoystick.deltaY = 0;
            }
        }
    }
    
    update() {
        // Update movement input
        this.updateMovement();
        
        // Update action inputs
        this.updateActions();
        
        // Update virtual joystick
        this.updateVirtualJoystick();
        
        // Clear single-frame input states
        this.clearFrameInputs();
    }
    
    updateMovement() {
        let moveX = 0;
        let moveY = 0;
        
        // Keyboard movement
        if (this.isKeyDown(CONFIG.INPUT.KEYS.MOVE_LEFT)) moveX -= 1;
        if (this.isKeyDown(CONFIG.INPUT.KEYS.MOVE_RIGHT)) moveX += 1;
        if (this.isKeyDown(CONFIG.INPUT.KEYS.MOVE_UP)) moveY -= 1;
        if (this.isKeyDown(CONFIG.INPUT.KEYS.MOVE_DOWN)) moveY += 1;
        
        // Virtual joystick movement (mobile)
        if (this.virtualJoystick.active) {
            moveX += this.virtualJoystick.deltaX;
            moveY += this.virtualJoystick.deltaY;
        }
        
        // Normalize diagonal movement
        if (moveX !== 0 && moveY !== 0) {
            const length = Math.sqrt(moveX * moveX + moveY * moveY);
            moveX /= length;
            moveY /= length;
        }
        
        this.movement.x = Utils.clamp(moveX, -1, 1);
        this.movement.y = Utils.clamp(moveY, -1, 1);
    }
    
    updateActions() {
        // Attack action - set to true when pressed, will be cleared by consumer
        if (this.isKeyPressed(CONFIG.INPUT.KEYS.ATTACK) || this.mouse.leftPressed) {
            this.actions.attack = true;
        }

        // Other actions
        this.actions.interact = this.isKeyPressed(CONFIG.INPUT.KEYS.INTERACT);
        this.actions.inventory = this.isKeyPressed(CONFIG.INPUT.KEYS.INVENTORY);
        this.actions.pause = this.isKeyPressed(CONFIG.INPUT.KEYS.PAUSE);
    }

    // Method for consumers to clear attack action after processing
    clearAttackAction() {
        this.actions.attack = false;
    }
    
    updateVirtualJoystick() {
        if (this.virtualJoystick.active) {
            const deltaX = this.virtualJoystick.currentX - this.virtualJoystick.centerX;
            const deltaY = this.virtualJoystick.currentY - this.virtualJoystick.centerY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const maxDistance = CONFIG.INPUT.VIRTUAL_JOYSTICK_SIZE / 2;
            
            if (distance > CONFIG.INPUT.VIRTUAL_JOYSTICK_DEADZONE) {
                // Normalize and apply sensitivity
                const normalizedDistance = Math.min(distance, maxDistance) / maxDistance;
                this.virtualJoystick.deltaX = (deltaX / distance) * normalizedDistance * CONFIG.INPUT.TOUCH_MOVE_SENSITIVITY;
                this.virtualJoystick.deltaY = (deltaY / distance) * normalizedDistance * CONFIG.INPUT.TOUCH_MOVE_SENSITIVITY;
            } else {
                this.virtualJoystick.deltaX = 0;
                this.virtualJoystick.deltaY = 0;
            }
        }
    }
    
    clearFrameInputs() {
        // Clear single-frame key states
        this.keysPressed = {};
        this.keysReleased = {};
        
        // Clear single-frame mouse states
        this.mouse.leftPressed = false;
        this.mouse.rightPressed = false;
        this.mouse.leftReleased = false;
        this.mouse.rightReleased = false;
        
        // Clear single-frame action states (attack is cleared by player after processing)
        // this.actions.attack = false; // Don't clear here - let player handle it
    }
    
    // Helper methods
    isKeyDown(keyArray) {
        return keyArray.some(key => this.keys[key]);
    }
    
    isKeyPressed(keyArray) {
        return keyArray.some(key => this.keysPressed[key]);
    }
    
    isKeyReleased(keyArray) {
        return keyArray.some(key => this.keysReleased[key]);
    }
    
    isGameKey(code) {
        const allGameKeys = [
            ...CONFIG.INPUT.KEYS.MOVE_UP,
            ...CONFIG.INPUT.KEYS.MOVE_DOWN,
            ...CONFIG.INPUT.KEYS.MOVE_LEFT,
            ...CONFIG.INPUT.KEYS.MOVE_RIGHT,
            ...CONFIG.INPUT.KEYS.ATTACK,
            ...CONFIG.INPUT.KEYS.INTERACT,
            ...CONFIG.INPUT.KEYS.INVENTORY,
            ...CONFIG.INPUT.KEYS.PAUSE
        ];
        return allGameKeys.includes(code);
    }
    
    getMovement() {
        return { x: this.movement.x, y: this.movement.y };
    }
    
    getMouseWorldPosition() {
        return { x: this.mouse.worldX, y: this.mouse.worldY };
    }
    
    isAttacking() {
        return this.actions.attack;
    }
    
    isInteracting() {
        return this.actions.interact;
    }
    
    isInventoryToggled() {
        return this.actions.inventory;
    }
    
    isPauseToggled() {
        return this.actions.pause;
    }
}

// Create global input manager instance
window.inputManager = new InputManager();
