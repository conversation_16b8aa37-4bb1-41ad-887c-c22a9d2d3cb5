/**
 * UTILITY FUNCTIONS
 * 
 * Common utility functions used throughout the game.
 * These provide mathematical operations, collision detection,
 * and other helper functions.
 */

const Utils = {
    
    /**
     * Generate a random number between min and max (inclusive)
     */
    random(min, max) {
        return Math.random() * (max - min) + min;
    },
    
    /**
     * Generate a random integer between min and max (inclusive)
     */
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    /**
     * Choose a random element from an array
     */
    randomChoice(array) {
        return array[Math.floor(Math.random() * array.length)];
    },
    
    /**
     * Calculate distance between two points
     */
    distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    },
    
    /**
     * Calculate squared distance (faster when you don't need exact distance)
     */
    distanceSquared(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return dx * dx + dy * dy;
    },
    
    /**
     * Calculate angle between two points in radians
     */
    angle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    },
    
    /**
     * Convert radians to degrees
     */
    toDegrees(radians) {
        return radians * (180 / Math.PI);
    },
    
    /**
     * Convert degrees to radians
     */
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    },
    
    /**
     * Normalize an angle to be between 0 and 2π
     */
    normalizeAngle(angle) {
        while (angle < 0) angle += Math.PI * 2;
        while (angle >= Math.PI * 2) angle -= Math.PI * 2;
        return angle;
    },
    
    /**
     * Linear interpolation between two values
     */
    lerp(start, end, factor) {
        return start + (end - start) * factor;
    },
    
    /**
     * Clamp a value between min and max
     */
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },
    
    /**
     * Check if two rectangles are colliding (AABB collision)
     */
    rectCollision(x1, y1, w1, h1, x2, y2, w2, h2) {
        return x1 < x2 + w2 &&
               x1 + w1 > x2 &&
               y1 < y2 + h2 &&
               y1 + h1 > y2;
    },
    
    /**
     * Check if a point is inside a rectangle
     */
    pointInRect(px, py, rx, ry, rw, rh) {
        return px >= rx && px <= rx + rw && py >= ry && py <= ry + rh;
    },
    
    /**
     * Check if two circles are colliding
     */
    circleCollision(x1, y1, r1, x2, y2, r2) {
        const distance = this.distance(x1, y1, x2, y2);
        return distance < r1 + r2;
    },
    
    /**
     * Get the overlap amount between two rectangles
     */
    getRectOverlap(x1, y1, w1, h1, x2, y2, w2, h2) {
        const overlapX = Math.min(x1 + w1, x2 + w2) - Math.max(x1, x2);
        const overlapY = Math.min(y1 + h1, y2 + h2) - Math.max(y1, y2);
        
        if (overlapX > 0 && overlapY > 0) {
            return { x: overlapX, y: overlapY };
        }
        return null;
    },
    
    /**
     * Move a point towards a target by a certain distance
     */
    moveTowards(fromX, fromY, toX, toY, distance) {
        const angle = this.angle(fromX, fromY, toX, toY);
        return {
            x: fromX + Math.cos(angle) * distance,
            y: fromY + Math.sin(angle) * distance
        };
    },
    
    /**
     * Calculate damage with variance and critical hits
     */
    calculateDamage(baseDamage, defense = 0) {
        // Apply damage variance
        const variance = CONFIG.COMBAT.DAMAGE_VARIANCE;
        const randomFactor = 1 + this.random(-variance, variance);
        let damage = baseDamage * randomFactor;
        
        // Check for critical hit
        if (Math.random() < CONFIG.COMBAT.CRITICAL_HIT_CHANCE) {
            damage *= CONFIG.COMBAT.CRITICAL_HIT_MULTIPLIER;
        }
        
        // Apply defense reduction
        const defenseReduction = defense * CONFIG.COMBAT.DEFENSE_REDUCTION_FACTOR;
        const maxReduction = damage * CONFIG.COMBAT.MAX_DEFENSE_REDUCTION;
        const actualReduction = Math.min(defenseReduction, maxReduction);
        
        damage = Math.max(1, damage - actualReduction); // Minimum 1 damage
        
        return Math.round(damage);
    },
    
    /**
     * Convert world coordinates to tile coordinates
     */
    worldToTile(worldX, worldY) {
        return {
            x: Math.floor(worldX / CONFIG.GAME.SCALED_TILE_SIZE),
            y: Math.floor(worldY / CONFIG.GAME.SCALED_TILE_SIZE)
        };
    },
    
    /**
     * Convert tile coordinates to world coordinates
     */
    tileToWorld(tileX, tileY) {
        return {
            x: tileX * CONFIG.GAME.SCALED_TILE_SIZE,
            y: tileY * CONFIG.GAME.SCALED_TILE_SIZE
        };
    },
    
    /**
     * Check if a tile coordinate is valid within the dungeon bounds
     */
    isValidTile(tileX, tileY) {
        return tileX >= 0 && tileX < CONFIG.DUNGEON.FLOOR_WIDTH &&
               tileY >= 0 && tileY < CONFIG.DUNGEON.FLOOR_HEIGHT;
    },
    
    /**
     * Format a number with commas for display
     */
    formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    
    /**
     * Get a color based on rarity
     */
    getRarityColor(rarity) {
        return CONFIG.ITEMS.RARITY_COLORS[rarity] || CONFIG.ITEMS.RARITY_COLORS.COMMON;
    },
    
    /**
     * Debounce function to limit how often a function can be called
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * Deep clone an object
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== "object") return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === "object") {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },
    
    /**
     * Check if the device is mobile
     */
    isMobile() {
        return window.innerWidth <= CONFIG.UI.MOBILE_BREAKPOINT ||
               /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },
    
    /**
     * Get appropriate UI scale based on screen size
     */
    getUIScale() {
        const width = window.innerWidth;
        if (width < 600) return CONFIG.UI.UI_SCALE_FACTORS.SMALL;
        if (width > 1200) return CONFIG.UI.UI_SCALE_FACTORS.LARGE;
        return CONFIG.UI.UI_SCALE_FACTORS.MEDIUM;
    },
    
    /**
     * Create a simple 2D vector
     */
    createVector(x = 0, y = 0) {
        return {
            x: x,
            y: y,
            add(other) {
                this.x += other.x;
                this.y += other.y;
                return this;
            },
            subtract(other) {
                this.x -= other.x;
                this.y -= other.y;
                return this;
            },
            multiply(scalar) {
                this.x *= scalar;
                this.y *= scalar;
                return this;
            },
            normalize() {
                const length = Math.sqrt(this.x * this.x + this.y * this.y);
                if (length > 0) {
                    this.x /= length;
                    this.y /= length;
                }
                return this;
            },
            length() {
                return Math.sqrt(this.x * this.x + this.y * this.y);
            }
        };
    }
};

// Make Utils globally available
window.Utils = Utils;
