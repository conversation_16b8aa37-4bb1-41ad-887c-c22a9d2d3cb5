/**
 * SOUND MANAGER
 * 
 * Manages audio playback, volume control, and sound effects.
 * Provides a simple interface for playing sounds throughout the game.
 */

class SoundManager {
    constructor() {
        this.sounds = new Map();
        this.music = new Map();
        this.masterVolume = CONFIG.AUDIO.MASTER_VOLUME;
        this.sfxVolume = CONFIG.AUDIO.SFX_VOLUME;
        this.musicVolume = CONFIG.AUDIO.MUSIC_VOLUME;
        this.currentMusic = null;
        this.musicFadeInterval = null;
        this.enabled = true;
        
        // Check for audio support
        this.audioContext = null;
        this.initializeAudioContext();
    }
    
    initializeAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Web Audio API not supported, falling back to HTML5 audio');
        }
    }
    
    async loadSound(name, path) {
        try {
            const audio = new Audio();
            audio.preload = 'auto';
            
            return new Promise((resolve, reject) => {
                audio.oncanplaythrough = () => {
                    this.sounds.set(name, audio);
                    resolve(audio);
                };
                audio.onerror = () => {
                    console.warn(`Failed to load sound: ${path}`);
                    reject(new Error(`Failed to load sound: ${path}`));
                };
                audio.src = path;
            });
        } catch (error) {
            console.warn(`Error loading sound ${name}:`, error);
        }
    }
    
    async loadMusic(name, path) {
        try {
            const audio = new Audio();
            audio.preload = 'auto';
            audio.loop = true;
            
            return new Promise((resolve, reject) => {
                audio.oncanplaythrough = () => {
                    this.music.set(name, audio);
                    resolve(audio);
                };
                audio.onerror = () => {
                    console.warn(`Failed to load music: ${path}`);
                    reject(new Error(`Failed to load music: ${path}`));
                };
                audio.src = path;
            });
        } catch (error) {
            console.warn(`Error loading music ${name}:`, error);
        }
    }
    
    playSound(name, volume = 1.0, pitch = 1.0) {
        if (!this.enabled) return;
        
        const sound = this.sounds.get(name);
        if (!sound) {
            console.warn(`Sound '${name}' not found`);
            return;
        }
        
        try {
            // Clone the audio for overlapping sounds
            const audioClone = sound.cloneNode();
            audioClone.volume = this.masterVolume * this.sfxVolume * volume;
            audioClone.playbackRate = pitch;
            
            // Clean up after playing
            audioClone.addEventListener('ended', () => {
                audioClone.remove();
            });
            
            audioClone.play().catch(error => {
                console.warn(`Failed to play sound '${name}':`, error);
            });
            
            return audioClone;
        } catch (error) {
            console.warn(`Error playing sound '${name}':`, error);
        }
    }
    
    playMusic(name, fadeIn = false, fadeTime = 1000) {
        if (!this.enabled) return;
        
        const music = this.music.get(name);
        if (!music) {
            console.warn(`Music '${name}' not found`);
            return;
        }
        
        // Stop current music
        if (this.currentMusic) {
            this.stopMusic(fadeIn, fadeTime);
        }
        
        this.currentMusic = music;
        music.volume = fadeIn ? 0 : this.masterVolume * this.musicVolume;
        
        music.play().catch(error => {
            console.warn(`Failed to play music '${name}':`, error);
        });
        
        if (fadeIn) {
            this.fadeInMusic(fadeTime);
        }
    }
    
    stopMusic(fadeOut = false, fadeTime = 1000) {
        if (!this.currentMusic) return;
        
        if (fadeOut) {
            this.fadeOutMusic(fadeTime);
        } else {
            this.currentMusic.pause();
            this.currentMusic.currentTime = 0;
            this.currentMusic = null;
        }
    }
    
    fadeInMusic(duration) {
        if (!this.currentMusic) return;
        
        const targetVolume = this.masterVolume * this.musicVolume;
        const steps = 50;
        const stepTime = duration / steps;
        const volumeStep = targetVolume / steps;
        
        let currentStep = 0;
        this.musicFadeInterval = setInterval(() => {
            currentStep++;
            this.currentMusic.volume = Math.min(volumeStep * currentStep, targetVolume);
            
            if (currentStep >= steps) {
                clearInterval(this.musicFadeInterval);
                this.musicFadeInterval = null;
            }
        }, stepTime);
    }
    
    fadeOutMusic(duration) {
        if (!this.currentMusic) return;
        
        const startVolume = this.currentMusic.volume;
        const steps = 50;
        const stepTime = duration / steps;
        const volumeStep = startVolume / steps;
        
        let currentStep = 0;
        this.musicFadeInterval = setInterval(() => {
            currentStep++;
            this.currentMusic.volume = Math.max(startVolume - (volumeStep * currentStep), 0);
            
            if (currentStep >= steps) {
                this.currentMusic.pause();
                this.currentMusic.currentTime = 0;
                this.currentMusic = null;
                clearInterval(this.musicFadeInterval);
                this.musicFadeInterval = null;
            }
        }, stepTime);
    }
    
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.currentMusic) {
            this.currentMusic.volume = this.masterVolume * this.musicVolume;
        }
    }
    
    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
    }
    
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        if (this.currentMusic) {
            this.currentMusic.volume = this.masterVolume * this.musicVolume;
        }
    }
    
    mute() {
        this.enabled = false;
        if (this.currentMusic) {
            this.currentMusic.pause();
        }
    }
    
    unmute() {
        this.enabled = true;
        if (this.currentMusic) {
            this.currentMusic.play().catch(error => {
                console.warn('Failed to resume music:', error);
            });
        }
    }
    
    toggle() {
        if (this.enabled) {
            this.mute();
        } else {
            this.unmute();
        }
    }
    
    // Convenience methods for common game sounds
    playPlayerAttack() {
        this.playSound('player_attack', 0.8, Utils.random(0.9, 1.1));
    }
    
    playPlayerHit() {
        this.playSound('player_hit', 1.0, Utils.random(0.8, 1.2));
    }
    
    playEnemyHit() {
        this.playSound('enemy_hit', 0.7, Utils.random(0.9, 1.1));
    }
    
    playItemPickup() {
        this.playSound('item_pickup', 0.6, Utils.random(1.0, 1.2));
    }
    
    playLevelUp() {
        this.playSound('level_up', 1.0);
    }
    
    playDoorOpen() {
        this.playSound('door_open', 0.8);
    }
    
    // Load all sounds from manifest
    async loadAllSounds() {
        if (!window.assetLoader || !window.assetLoader.manifest) {
            console.warn('Asset loader or manifest not available');
            return;
        }
        
        const manifest = window.assetLoader.manifest;
        const loadPromises = [];
        
        // Load sound effects
        if (manifest.sounds) {
            for (const [name, path] of Object.entries(manifest.sounds)) {
                loadPromises.push(this.loadSound(name, path));
            }
        }
        
        // Load music
        if (manifest.music) {
            for (const [name, config] of Object.entries(manifest.music)) {
                const path = typeof config === 'string' ? config : config.path;
                loadPromises.push(this.loadMusic(name, path));
            }
        }
        
        try {
            await Promise.all(loadPromises);
            console.log('All sounds loaded successfully');
        } catch (error) {
            console.warn('Some sounds failed to load:', error);
        }
    }
    
    // Get loading progress
    getLoadingProgress() {
        const totalSounds = this.sounds.size + this.music.size;
        const loadedSounds = Array.from(this.sounds.values()).filter(audio => audio.readyState >= 3).length;
        const loadedMusic = Array.from(this.music.values()).filter(audio => audio.readyState >= 3).length;
        
        return totalSounds > 0 ? (loadedSounds + loadedMusic) / totalSounds : 1;
    }
}

// Create global sound manager instance
window.soundManager = new SoundManager();

// Export for use in other modules
window.SoundManager = SoundManager;
