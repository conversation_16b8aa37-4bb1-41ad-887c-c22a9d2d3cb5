/**
 * ENTITY SYSTEM
 * 
 * Base entity class and common entity behaviors.
 * All game objects (player, enemies, items) inherit from Entity.
 */

class Entity {
    constructor(x, y, width, height) {
        // Position and dimensions
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        
        // Velocity and movement
        this.velocityX = 0;
        this.velocityY = 0;
        this.speed = 100;
        
        // Health and combat
        this.health = 100;
        this.maxHealth = 100;
        this.attack = 10;
        this.defense = 0;
        
        // Status and state
        this.alive = true;
        this.invulnerable = false;
        this.invulnerabilityTimer = 0;
        
        // Rendering
        this.sprite = null;
        this.visible = true;
        
        // Collision
        this.hitbox = {
            offsetX: 0,
            offsetY: 0,
            width: width,
            height: height
        };
        
        // Unique identifier
        this.id = this.generateId();
        
        // Entity type for identification
        this.type = 'entity';
    }
    
    generateId() {
        return Math.random().toString(36).substring(2, 11);
    }
    
    update(deltaTime) {
        // Store old position for collision resolution
        const oldX = this.x;
        const oldY = this.y;

        // Update position based on velocity
        this.x += this.velocityX * (deltaTime / 1000);
        this.y += this.velocityY * (deltaTime / 1000);

        // Check collision with dungeon walls if dungeon exists
        if (window.game && window.game.dungeon) {
            this.checkDungeonCollision(oldX, oldY);
        }

        // Update invulnerability
        if (this.invulnerable) {
            this.invulnerabilityTimer -= deltaTime;
            if (this.invulnerabilityTimer <= 0) {
                this.invulnerable = false;
            }
        }

        // Check if entity should be removed
        if (this.health <= 0) {
            this.alive = false;
        }
    }
    
    render(renderer) {
        if (!this.visible) return;
        
        // Flash effect when invulnerable
        if (this.invulnerable && Math.floor(Date.now() / 100) % 2) {
            return; // Skip rendering every other frame
        }
        
        if (this.sprite) {
            renderer.drawSprite(this.sprite, this.x, this.y, this.width, this.height);
        } else {
            // Default rendering as colored rectangle
            renderer.drawRect(this.x, this.y, this.width, this.height, '#ff00ff');
        }
        
        // Draw health bar if damaged
        if (this.health < this.maxHealth && this.health > 0) {
            renderer.drawHealthBar(this.x, this.y, this.health, this.maxHealth, this.width);
        }
    }
    
    takeDamage(damage, attacker = null) {
        if (!this.alive || this.invulnerable) return false;
        
        // Calculate actual damage with defense
        const actualDamage = Utils.calculateDamage(damage, this.defense);
        
        // Apply damage
        this.health -= actualDamage;
        this.health = Math.max(0, this.health);
        
        // Add damage text
        if (window.game && window.game.renderer) {
            const isCritical = actualDamage > damage; // Simple critical detection
            window.game.renderer.addDamageText(
                this.x + this.width / 2,
                this.y,
                actualDamage,
                isCritical
            );
        }

        // Add hit effect particles
        if (window.particlePool) {
            window.particlePool.createBloodSplatter(
                this.x + this.width / 2,
                this.y + this.height / 2,
                3
            );
        }
        
        // Make invulnerable briefly
        this.makeInvulnerable(CONFIG.PLAYER.INVINCIBILITY_FRAMES);
        
        // Apply knockback if attacker exists
        if (attacker) {
            this.applyKnockback(attacker);
        }
        
        // Trigger death if health reaches 0
        if (this.health <= 0) {
            this.onDeath();
        }
        
        return true;
    }
    
    heal(amount) {
        if (!this.alive) return;
        
        this.health += amount;
        this.health = Math.min(this.health, this.maxHealth);
        
        // Add healing text
        if (window.game && window.game.renderer) {
            window.game.renderer.addDamageText(
                this.x + this.width / 2,
                this.y,
                `+${amount}`,
                false
            );
        }
    }
    
    makeInvulnerable(duration) {
        this.invulnerable = true;
        this.invulnerabilityTimer = duration;
    }
    
    applyKnockback(attacker) {
        const angle = Utils.angle(attacker.x, attacker.y, this.x, this.y);
        const force = CONFIG.PLAYER.KNOCKBACK_FORCE;
        
        this.velocityX += Math.cos(angle) * force;
        this.velocityY += Math.sin(angle) * force;
    }
    
    onDeath() {
        this.alive = false;
        // Override in subclasses for specific death behavior
    }
    
    getCenter() {
        return {
            x: this.x + this.width / 2,
            y: this.y + this.height / 2
        };
    }
    
    getHitbox() {
        return {
            x: this.x + this.hitbox.offsetX,
            y: this.y + this.hitbox.offsetY,
            width: this.hitbox.width,
            height: this.hitbox.height
        };
    }
    
    isCollidingWith(other) {
        const thisHitbox = this.getHitbox();
        const otherHitbox = other.getHitbox();
        
        return Utils.rectCollision(
            thisHitbox.x, thisHitbox.y, thisHitbox.width, thisHitbox.height,
            otherHitbox.x, otherHitbox.y, otherHitbox.width, otherHitbox.height
        );
    }
    
    distanceTo(other) {
        const thisCenter = this.getCenter();
        const otherCenter = other.getCenter();
        return Utils.distance(thisCenter.x, thisCenter.y, otherCenter.x, otherCenter.y);
    }
    
    angleTo(other) {
        const thisCenter = this.getCenter();
        const otherCenter = other.getCenter();
        return Utils.angle(thisCenter.x, thisCenter.y, otherCenter.x, otherCenter.y);
    }
    
    moveTowards(target, speed, deltaTime) {
        const angle = this.angleTo(target);
        const moveDistance = speed * (deltaTime / 1000);
        
        this.x += Math.cos(angle) * moveDistance;
        this.y += Math.sin(angle) * moveDistance;
    }
    
    setPosition(x, y) {
        this.x = x;
        this.y = y;
    }
    
    setVelocity(vx, vy) {
        this.velocityX = vx;
        this.velocityY = vy;
    }
    
    addVelocity(vx, vy) {
        this.velocityX += vx;
        this.velocityY += vy;
    }
    
    applyFriction(friction) {
        this.velocityX *= friction;
        this.velocityY *= friction;
        
        // Stop very small velocities to prevent jitter
        if (Math.abs(this.velocityX) < 1) this.velocityX = 0;
        if (Math.abs(this.velocityY) < 1) this.velocityY = 0;
    }
    
    isOnScreen(camera, screenWidth, screenHeight) {
        return this.x + this.width > camera.x &&
               this.x < camera.x + screenWidth &&
               this.y + this.height > camera.y &&
               this.y < camera.y + screenHeight;
    }
    
    // Utility method to check if entity is within a certain distance
    isWithinRange(target, range) {
        return this.distanceTo(target) <= range;
    }
    
    // Method to face towards a target
    faceTarget(target) {
        // This can be used for sprite rotation in more advanced implementations
        this.facingAngle = this.angleTo(target);
    }
    
    // Clone method for creating copies
    clone() {
        const clone = new Entity(this.x, this.y, this.width, this.height);
        clone.health = this.health;
        clone.maxHealth = this.maxHealth;
        clone.attack = this.attack;
        clone.defense = this.defense;
        clone.speed = this.speed;
        clone.sprite = this.sprite;
        clone.type = this.type;
        return clone;
    }
    
    // Serialize entity data for saving
    serialize() {
        return {
            id: this.id,
            type: this.type,
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height,
            health: this.health,
            maxHealth: this.maxHealth,
            attack: this.attack,
            defense: this.defense,
            speed: this.speed,
            alive: this.alive
        };
    }
    
    // Restore entity from serialized data
    deserialize(data) {
        this.id = data.id;
        this.type = data.type;
        this.x = data.x;
        this.y = data.y;
        this.width = data.width;
        this.height = data.height;
        this.health = data.health;
        this.maxHealth = data.maxHealth;
        this.attack = data.attack;
        this.defense = data.defense;
        this.speed = data.speed;
        this.alive = data.alive;
    }

    // Check collision with dungeon walls
    checkDungeonCollision(oldX, oldY) {
        // Try horizontal movement first
        const horizontalCollision = this.checkCollisionAtPosition(this.x, oldY);
        if (horizontalCollision) {
            this.x = oldX; // Revert horizontal movement
            this.velocityX = 0;
        }

        // Try vertical movement
        const verticalCollision = this.checkCollisionAtPosition(this.x, this.y);
        if (verticalCollision) {
            this.y = oldY; // Revert vertical movement
            this.velocityY = 0;
        }
    }

    checkCollisionAtPosition(x, y) {
        const dungeon = window.game.dungeon;
        const tileSize = CONFIG.GAME.SCALED_TILE_SIZE;

        // Calculate hitbox at the given position
        const hitboxX = x + this.hitbox.offsetX;
        const hitboxY = y + this.hitbox.offsetY;
        const hitboxWidth = this.hitbox.width;
        const hitboxHeight = this.hitbox.height;

        // Convert to tile coordinates with small margin to prevent edge cases
        const leftTile = Math.floor((hitboxX + 1) / tileSize);
        const rightTile = Math.floor((hitboxX + hitboxWidth - 2) / tileSize);
        const topTile = Math.floor((hitboxY + 1) / tileSize);
        const bottomTile = Math.floor((hitboxY + hitboxHeight - 2) / tileSize);

        // Check for wall collisions
        for (let tileX = leftTile; tileX <= rightTile; tileX++) {
            for (let tileY = topTile; tileY <= bottomTile; tileY++) {
                if (dungeon.getTile(tileX, tileY) === CONFIG.DUNGEON.TILE_TYPES.WALL) {
                    return true;
                }
            }
        }

        return false;
    }
}

// Export for use in other modules
window.Entity = Entity;
