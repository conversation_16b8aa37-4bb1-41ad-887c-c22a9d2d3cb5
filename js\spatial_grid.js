/**
 * SPATIAL PARTITIONING SYSTEM
 * 
 * Grid-based spatial partitioning for efficient collision detection
 * and entity queries. Reduces collision checks from O(n²) to O(n).
 */

class SpatialGrid {
    constructor(width, height, cellSize) {
        this.width = width;
        this.height = height;
        this.cellSize = cellSize;
        this.cols = Math.ceil(width / cellSize);
        this.rows = Math.ceil(height / cellSize);
        
        // Grid of cells, each containing arrays of entities
        this.grid = [];
        this.entityCells = new Map(); // Track which cells each entity is in
        
        this.initializeGrid();
    }
    
    initializeGrid() {
        this.grid = [];
        for (let i = 0; i < this.cols * this.rows; i++) {
            this.grid[i] = [];
        }
    }
    
    clear() {
        for (let i = 0; i < this.grid.length; i++) {
            this.grid[i].length = 0;
        }
        this.entityCells.clear();
    }
    
    getCellIndex(x, y) {
        const col = Math.floor(x / this.cellSize);
        const row = Math.floor(y / this.cellSize);
        
        if (col < 0 || col >= this.cols || row < 0 || row >= this.rows) {
            return -1;
        }
        
        return row * this.cols + col;
    }
    
    getCellCoords(x, y) {
        return {
            col: Math.floor(x / this.cellSize),
            row: Math.floor(y / this.cellSize)
        };
    }
    
    insert(entity) {
        const cells = this.getEntityCells(entity);
        this.entityCells.set(entity.id, cells);
        
        for (const cellIndex of cells) {
            if (cellIndex >= 0 && cellIndex < this.grid.length) {
                this.grid[cellIndex].push(entity);
            }
        }
    }
    
    remove(entity) {
        const cells = this.entityCells.get(entity.id);
        if (!cells) return;
        
        for (const cellIndex of cells) {
            if (cellIndex >= 0 && cellIndex < this.grid.length) {
                const cell = this.grid[cellIndex];
                const index = cell.indexOf(entity);
                if (index !== -1) {
                    cell.splice(index, 1);
                }
            }
        }
        
        this.entityCells.delete(entity.id);
    }
    
    update(entity) {
        // Remove from old cells
        this.remove(entity);
        // Insert into new cells
        this.insert(entity);
    }
    
    getEntityCells(entity) {
        const cells = [];
        const hitbox = entity.getHitbox();
        
        const startCol = Math.floor(hitbox.x / this.cellSize);
        const endCol = Math.floor((hitbox.x + hitbox.width - 1) / this.cellSize);
        const startRow = Math.floor(hitbox.y / this.cellSize);
        const endRow = Math.floor((hitbox.y + hitbox.height - 1) / this.cellSize);
        
        for (let row = startRow; row <= endRow; row++) {
            for (let col = startCol; col <= endCol; col++) {
                if (col >= 0 && col < this.cols && row >= 0 && row < this.rows) {
                    cells.push(row * this.cols + col);
                }
            }
        }
        
        return cells;
    }
    
    queryRange(x, y, width, height) {
        const entities = new Set();
        
        const startCol = Math.floor(x / this.cellSize);
        const endCol = Math.floor((x + width - 1) / this.cellSize);
        const startRow = Math.floor(y / this.cellSize);
        const endRow = Math.floor((y + height - 1) / this.cellSize);
        
        for (let row = startRow; row <= endRow; row++) {
            for (let col = startCol; col <= endCol; col++) {
                if (col >= 0 && col < this.cols && row >= 0 && row < this.rows) {
                    const cellIndex = row * this.cols + col;
                    const cell = this.grid[cellIndex];
                    
                    for (const entity of cell) {
                        entities.add(entity);
                    }
                }
            }
        }
        
        return Array.from(entities);
    }
    
    queryPoint(x, y) {
        const cellIndex = this.getCellIndex(x, y);
        if (cellIndex === -1) return [];
        
        return [...this.grid[cellIndex]];
    }
    
    queryRadius(centerX, centerY, radius) {
        const entities = new Set();
        const radiusSquared = radius * radius;
        
        // Query a square area around the circle
        const queryEntities = this.queryRange(
            centerX - radius,
            centerY - radius,
            radius * 2,
            radius * 2
        );
        
        // Filter to only entities actually within the circle
        for (const entity of queryEntities) {
            const entityCenter = entity.getCenter();
            const dx = entityCenter.x - centerX;
            const dy = entityCenter.y - centerY;
            const distanceSquared = dx * dx + dy * dy;
            
            if (distanceSquared <= radiusSquared) {
                entities.add(entity);
            }
        }
        
        return Array.from(entities);
    }
    
    getNearbyEntities(entity, maxDistance = null) {
        const hitbox = entity.getHitbox();
        const searchRadius = maxDistance || Math.max(hitbox.width, hitbox.height);
        
        return this.queryRadius(
            hitbox.x + hitbox.width / 2,
            hitbox.y + hitbox.height / 2,
            searchRadius
        ).filter(other => other.id !== entity.id);
    }
    
    findCollisions(entity) {
        const nearby = this.getNearbyEntities(entity);
        const collisions = [];
        
        for (const other of nearby) {
            if (entity.isCollidingWith(other)) {
                collisions.push(other);
            }
        }
        
        return collisions;
    }
    
    // Debug visualization
    render(renderer) {
        if (!CONFIG.GAME.DEBUG_MODE) return;
        
        renderer.ctx.strokeStyle = 'rgba(0, 255, 0, 0.3)';
        renderer.ctx.lineWidth = 1;
        
        // Draw grid lines
        for (let col = 0; col <= this.cols; col++) {
            const x = col * this.cellSize;
            renderer.ctx.beginPath();
            renderer.ctx.moveTo(x - renderer.camera.x, 0 - renderer.camera.y);
            renderer.ctx.lineTo(x - renderer.camera.x, this.height - renderer.camera.y);
            renderer.ctx.stroke();
        }
        
        for (let row = 0; row <= this.rows; row++) {
            const y = row * this.cellSize;
            renderer.ctx.beginPath();
            renderer.ctx.moveTo(0 - renderer.camera.x, y - renderer.camera.y);
            renderer.ctx.lineTo(this.width - renderer.camera.x, y - renderer.camera.y);
            renderer.ctx.stroke();
        }
        
        // Draw entity counts in cells
        renderer.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        renderer.ctx.font = '10px monospace';
        renderer.ctx.textAlign = 'center';
        
        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.cols; col++) {
                const cellIndex = row * this.cols + col;
                const entityCount = this.grid[cellIndex].length;
                
                if (entityCount > 0) {
                    const x = col * this.cellSize + this.cellSize / 2;
                    const y = row * this.cellSize + this.cellSize / 2;
                    
                    renderer.ctx.fillText(
                        entityCount.toString(),
                        x - renderer.camera.x,
                        y - renderer.camera.y
                    );
                }
            }
        }
        
        renderer.ctx.textAlign = 'left'; // Reset text alignment
    }
    
    getStats() {
        let totalEntities = 0;
        let occupiedCells = 0;
        let maxEntitiesPerCell = 0;
        
        for (let i = 0; i < this.grid.length; i++) {
            const cellCount = this.grid[i].length;
            totalEntities += cellCount;
            
            if (cellCount > 0) {
                occupiedCells++;
                maxEntitiesPerCell = Math.max(maxEntitiesPerCell, cellCount);
            }
        }
        
        return {
            totalCells: this.grid.length,
            occupiedCells,
            totalEntities,
            maxEntitiesPerCell,
            averageEntitiesPerOccupiedCell: occupiedCells > 0 ? totalEntities / occupiedCells : 0
        };
    }
}

// Export for use in other modules
window.SpatialGrid = SpatialGrid;
