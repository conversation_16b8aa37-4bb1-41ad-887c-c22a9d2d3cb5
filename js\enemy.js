/**
 * ENEMY CLASS
 * 
 * Handles enemy AI, behavior, and combat.
 */

class Enemy extends Entity {
    constructor(x, y, enemyType = 'goblin') {
        super(x, y, CONFIG.GAME.SCALED_TILE_SIZE, CONFIG.GAME.SCALED_TILE_SIZE);
        
        this.enemyType = enemyType;
        this.config = CONFIG.ENEMIES[enemyType.toUpperCase()];
        
        if (!this.config) {
            console.warn(`Unknown enemy type: ${enemyType}, using GOBLIN`);
            this.config = CONFIG.ENEMIES.GOBLIN;
        }
        
        // Set stats from config
        this.health = this.config.HEALTH;
        this.maxHealth = this.config.HEALTH;
        this.attack = this.config.ATTACK;
        this.defense = this.config.DEFENSE;
        this.speed = this.config.SPEED;
        this.expReward = this.config.EXP_REWARD;
        
        // AI state
        this.aiState = 'wandering'; // wandering, chasing, attacking, retreating
        this.target = null;
        this.lastSeenPlayerPos = null;
        this.chaseTimer = 0;
        this.attackCooldown = 0;
        this.wanderTimer = 0;
        this.wanderDirection = Math.random() * Math.PI * 2;
        
        // Visual
        this.sprite = enemyType;
        this.type = 'enemy';
        
        // Hitbox
        this.hitbox = {
            offsetX: (this.width - this.config.HITBOX_WIDTH) / 2,
            offsetY: (this.height - this.config.HITBOX_HEIGHT) / 2,
            width: this.config.HITBOX_WIDTH,
            height: this.config.HITBOX_HEIGHT
        };
    }
    
    update(deltaTime, player, dungeon) {
        super.update(deltaTime);

        // Determine LOD level based on distance to player
        const distanceToPlayer = player ? this.distanceTo(player) : Infinity;
        const lodLevel = this.getLODLevel(distanceToPlayer);

        // Update AI with LOD considerations
        this.updateAI(deltaTime, player, lodLevel);

        // Update timers
        this.updateTimers(deltaTime);

        // Apply movement
        this.applyMovement();

        // Apply friction
        this.applyFriction(0.9);
    }

    getLODLevel(distance) {
        // LOD levels:
        // 0 = High detail (close)
        // 1 = Medium detail
        // 2 = Low detail (far)
        // 3 = Minimal detail (very far)

        if (distance < CONFIG.PERFORMANCE.ENTITY_CULL_DISTANCE * 0.3) {
            return 0; // High detail
        } else if (distance < CONFIG.PERFORMANCE.ENTITY_CULL_DISTANCE * 0.6) {
            return 1; // Medium detail
        } else if (distance < CONFIG.PERFORMANCE.ENTITY_CULL_DISTANCE) {
            return 2; // Low detail
        } else {
            return 3; // Minimal detail
        }
    }
    
    updateAI(deltaTime, player, lodLevel = 0) {
        if (!player || !player.alive) return;

        // Skip AI updates for very distant enemies
        if (lodLevel >= 3) {
            return;
        }

        // Reduce update frequency based on LOD level
        const updateChance = [1.0, 0.8, 0.5, 0.1][lodLevel];
        if (Math.random() > updateChance) {
            return;
        }

        const distanceToPlayer = this.distanceTo(player);

        // Skip expensive line of sight checks for distant enemies
        const canSeePlayer = lodLevel <= 1 ? this.canSeePlayer(player) : true;

        switch (this.aiState) {
            case 'wandering':
                this.handleWandering(deltaTime, lodLevel);
                if (canSeePlayer && distanceToPlayer <= this.config.DETECTION_RANGE) {
                    this.startChasing(player);
                }
                break;

            case 'chasing':
                this.handleChasing(deltaTime, player, distanceToPlayer, lodLevel);
                break;

            case 'attacking':
                this.handleAttacking(deltaTime, player, distanceToPlayer, lodLevel);
                break;

            case 'retreating':
                this.handleRetreating(player, lodLevel);
                break;
        }
    }
    
    handleWandering(deltaTime, lodLevel = 0) {
        // Reduce wander frequency for distant enemies
        const wanderSpeedMultiplier = [1.0, 0.8, 0.5, 0.2][lodLevel];

        this.wanderTimer -= deltaTime;

        if (this.wanderTimer <= 0) {
            // Change direction
            this.wanderDirection = Math.random() * Math.PI * 2;
            this.wanderTimer = this.config.WANDER_CHANGE_INTERVAL * (1 + lodLevel);
        }

        // Move in wander direction with reduced speed for distant enemies
        const moveSpeed = this.config.WANDER_SPEED * wanderSpeedMultiplier;
        this.velocityX = Math.cos(this.wanderDirection) * moveSpeed;
        this.velocityY = Math.sin(this.wanderDirection) * moveSpeed;
    }
    
    handleChasing(deltaTime, player, distance, lodLevel = 0) {
        this.target = player;
        this.lastSeenPlayerPos = { x: player.x, y: player.y };

        if (distance <= this.config.ATTACK_RANGE) {
            this.aiState = 'attacking';
            return;
        }

        if (distance > this.config.DETECTION_RANGE) {
            this.chaseTimer -= deltaTime;
            if (this.chaseTimer <= 0) {
                this.aiState = 'wandering';
                this.target = null;
                return;
            }
        } else {
            this.chaseTimer = this.config.CHASE_DURATION;
        }

        // Move towards player with reduced speed for distant enemies
        const speedMultiplier = [1.0, 0.9, 0.7, 0.5][lodLevel];
        this.moveTowardsTarget(player, this.speed * speedMultiplier);
    }
    
    handleAttacking(deltaTime, player, distance, lodLevel = 0) {
        if (distance > this.config.ATTACK_RANGE) {
            this.aiState = 'chasing';
            return;
        }

        // Reduce attack frequency for distant enemies
        const attackCooldownMultiplier = [1.0, 1.2, 1.5, 2.0][lodLevel];

        if (this.attackCooldown <= 0) {
            this.performAttack(player);
            this.attackCooldown = this.config.ATTACK_COOLDOWN * attackCooldownMultiplier;
        }

        // Special behavior for ranged enemies
        if (this.enemyType === 'skeleton' && distance < this.config.RETREAT_DISTANCE) {
            this.aiState = 'retreating';
        }
    }

    handleRetreating(player, lodLevel = 0) {
        const distance = this.distanceTo(player);

        if (distance >= this.config.RETREAT_DISTANCE) {
            this.aiState = 'attacking';
            return;
        }

        // Move away from player with reduced speed for distant enemies
        const speedMultiplier = [1.0, 0.8, 0.6, 0.4][lodLevel];
        const angle = this.angleTo(player) + Math.PI; // Opposite direction
        this.velocityX = Math.cos(angle) * this.speed * speedMultiplier;
        this.velocityY = Math.sin(angle) * this.speed * speedMultiplier;
    }
    
    moveTowardsTarget(target, speed) {
        const angle = this.angleTo(target);
        this.velocityX = Math.cos(angle) * speed;
        this.velocityY = Math.sin(angle) * speed;
    }
    
    canSeePlayer(player) {
        if (!player || !window.game || !window.game.dungeon) return false;

        const startX = this.x + this.width / 2;
        const startY = this.y + this.height / 2;
        const endX = player.x + player.width / 2;
        const endY = player.y + player.height / 2;

        return this.raycastToTarget(startX, startY, endX, endY);
    }

    raycastToTarget(x1, y1, x2, y2) {
        const dungeon = window.game.dungeon;
        const tileSize = CONFIG.GAME.SCALED_TILE_SIZE;

        // Calculate the distance and direction
        const dx = x2 - x1;
        const dy = y2 - y1;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // If too close, always can see
        if (distance < tileSize / 2) return true;

        // Normalize direction
        const stepX = (dx / distance) * (tileSize / 4); // Quarter tile steps for accuracy
        const stepY = (dy / distance) * (tileSize / 4);

        let currentX = x1;
        let currentY = y1;
        const steps = Math.floor(distance / (tileSize / 4));

        for (let i = 0; i < steps; i++) {
            currentX += stepX;
            currentY += stepY;

            // Convert world coordinates to tile coordinates
            const tileX = Math.floor(currentX / tileSize);
            const tileY = Math.floor(currentY / tileSize);

            // Check if current tile is a wall
            if (dungeon.getTile(tileX, tileY) === CONFIG.DUNGEON.TILE_TYPES.WALL) {
                return false; // Line of sight blocked
            }
        }

        return true; // Clear line of sight
    }
    
    performAttack(target) {
        console.log(`${this.enemyType} attacks!`);
        
        // Store attack info for combat system
        this.currentAttack = {
            target: target,
            damage: this.attack,
            range: this.config.ATTACK_RANGE,
            active: true
        };
    }
    
    updateTimers(deltaTime) {
        if (this.attackCooldown > 0) {
            this.attackCooldown -= deltaTime;
        }
    }
    
    applyMovement() {
        // Movement is now handled by the base Entity class update method
        // which includes proper dungeon collision detection
    }
    
    startChasing(player) {
        this.aiState = 'chasing';
        this.target = player;
        this.chaseTimer = this.config.CHASE_DURATION;
        console.log(`${this.enemyType} spotted player!`);
    }
    
    onDeath() {
        super.onDeath();

        // Give experience to player
        if (window.game && window.game.player) {
            window.game.player.gainExperience(this.expReward);

            // Record kill for statistics
            if (window.game.player.recordKill) {
                window.game.player.recordKill();
            }
        }
        
        console.log(`${this.enemyType} defeated! +${this.expReward} EXP`);
    }
}

window.Enemy = Enemy;
