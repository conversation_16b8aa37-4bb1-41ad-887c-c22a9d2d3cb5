# Dungeon Crawler - 2D Top-Down Action Game

A web-based 2D top-down dungeon crawler game built with HTML5 Canvas and JavaScript, designed for both desktop and mobile platforms targeting the .io games market.

## Features

- **Real-time Action Combat**: Attack in the direction of mouse cursor/touch with immediate response
- **Procedural Dungeon Generation**: Randomly generated rectangular rooms connected by corridors
- **Mobile-Friendly Controls**: Touch controls with virtual joystick for mobile devices
- **RPG Progression System**: Simplified stats (Attack, Defense, Health, Speed) with level progression
- **Multiple Enemy Types**: 3 different enemy types with unique AI behaviors
- **16x16 Pixel Art Style**: Consistent retro visual aesthetic
- **Comprehensive Configuration**: All gameplay variables centralized for easy balancing

## Technology Stack

- **Frontend**: HTML5 Canvas, JavaScript (ES6+)
- **Rendering**: Custom 2D renderer with pixel-perfect scaling
- **Input**: Unified input system supporting keyboard, mouse, and touch
- **Architecture**: Modular component-based system

## Game Systems

### Core Systems
- **Entity System**: Base class for all game objects with collision, health, and rendering
- **Input Manager**: Handles keyboard, mouse, and touch input with mobile support
- **Renderer**: 2D rendering system with camera, sprites, and effects
- **Combat System**: Real-time combat with damage calculation and effects

### Gameplay Systems
- **Player Character**: Movement, stats, leveling, and combat abilities
- **Enemy AI**: Multiple enemy types with different behaviors (melee, ranged, tank)
- **Dungeon Generation**: Procedural room and corridor generation
- **Item System**: Weapons, armor, and consumables with rarity system

## Configuration

All gameplay variables are centralized in `js/config.js` for easy balancing:

- **Player Stats**: Health, attack, defense, speed, leveling progression
- **Enemy Properties**: Health, damage, AI behavior parameters for each enemy type
- **Combat Settings**: Damage variance, critical hits, defense calculations
- **Dungeon Generation**: Room sizes, corridor settings, spawn rates
- **UI Settings**: Mobile responsiveness, scaling factors, control sizes

## Controls

### Desktop
- **WASD / Arrow Keys**: Movement
- **Mouse**: Aim attack direction
- **Left Click / Space**: Attack
- **E**: Interact
- **I / Tab**: Inventory
- **Escape**: Pause

### Mobile
- **Virtual Joystick**: Movement (left side of screen)
- **Touch**: Attack (right side of screen)
- **Attack Button**: Alternative attack control

## Getting Started

1. Clone or download the project
2. Open `index.html` in a web browser
3. Start playing immediately - no build process required!

## File Structure

```
├── index.html              # Main HTML file
├── js/
│   ├── config.js          # Game configuration and settings
│   ├── utils.js           # Utility functions
│   ├── input.js           # Input handling system
│   ├── renderer.js        # 2D rendering system
│   ├── entity.js          # Base entity class
│   ├── player.js          # Player character logic
│   ├── enemy.js           # Enemy AI and behavior
│   ├── combat.js          # Combat system
│   ├── dungeon.js         # Dungeon generation
│   ├── item.js            # Item and inventory system
│   └── main.js            # Main game loop and initialization
├── assets/
│   ├── sprites/           # Game sprites (16x16 pixels)
│   └── sounds/            # Audio files
└── README.md
```

## Development

### Adding New Enemy Types
1. Add enemy configuration to `CONFIG.ENEMIES` in `config.js`
2. Add spawn rate to `CONFIG.ENEMIES.SPAWN_RATES`
3. Create sprite in `assets/sprites/`
4. Enemy will automatically be available in game

### Adding New Items
1. Add item configuration to appropriate category in `CONFIG.ITEMS`
2. Set drop rates and rarity
3. Create sprite in `assets/sprites/`
4. Item will be available for spawning

### Modifying Game Balance
- Edit values in `config.js` - no code changes required
- All gameplay variables are documented with recommended ranges
- Changes take effect immediately on page refresh

## Browser Compatibility

- **Desktop**: Chrome, Firefox, Safari, Edge (modern versions)
- **Mobile**: iOS Safari, Chrome Mobile, Samsung Internet
- **Requirements**: HTML5 Canvas support, ES6 JavaScript

## Performance

- Optimized for 60 FPS on modern devices
- Efficient rendering with viewport culling
- Configurable performance settings in `CONFIG.PERFORMANCE`
- Mobile-optimized with touch-friendly controls

## Future Enhancements

- Map editor for custom dungeons
- Multiplayer support
- Additional enemy types and bosses
- More complex item system with inventory management
- Sound effects and background music
- Particle effects and visual polish
- Save/load game state

## License

This project is open source and available under the MIT License.
