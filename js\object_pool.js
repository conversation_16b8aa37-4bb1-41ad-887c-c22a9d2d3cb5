/**
 * OBJECT POOLING SYSTEM
 * 
 * Efficient object pooling for frequently created/destroyed objects
 * like damage texts, particles, and temporary effects.
 */

class ObjectPool {
    constructor(createFn, resetFn, initialSize = 10) {
        this.createFn = createFn;
        this.resetFn = resetFn;
        this.pool = [];
        this.active = [];
        
        // Pre-populate pool
        for (let i = 0; i < initialSize; i++) {
            this.pool.push(this.createFn());
        }
    }
    
    get() {
        let obj;
        if (this.pool.length > 0) {
            obj = this.pool.pop();
        } else {
            obj = this.createFn();
        }
        
        this.active.push(obj);
        return obj;
    }
    
    release(obj) {
        const index = this.active.indexOf(obj);
        if (index !== -1) {
            this.active.splice(index, 1);
            this.resetFn(obj);
            this.pool.push(obj);
        }
    }
    
    releaseAll() {
        while (this.active.length > 0) {
            const obj = this.active.pop();
            this.resetFn(obj);
            this.pool.push(obj);
        }
    }
    
    getActiveCount() {
        return this.active.length;
    }
    
    getPoolSize() {
        return this.pool.length;
    }
    
    getTotalSize() {
        return this.pool.length + this.active.length;
    }
}

class DamageTextPool {
    constructor() {
        this.pool = new ObjectPool(
            () => this.createDamageText(),
            (obj) => this.resetDamageText(obj),
            CONFIG.PERFORMANCE.MAX_DAMAGE_TEXTS
        );
    }
    
    createDamageText() {
        return {
            x: 0,
            y: 0,
            damage: 0,
            isCritical: false,
            life: 0,
            maxLife: 0,
            active: false
        };
    }
    
    resetDamageText(obj) {
        obj.x = 0;
        obj.y = 0;
        obj.damage = 0;
        obj.isCritical = false;
        obj.life = 0;
        obj.maxLife = 0;
        obj.active = false;
    }
    
    spawn(x, y, damage, isCritical = false) {
        const text = this.pool.get();
        text.x = x;
        text.y = y;
        text.damage = damage;
        text.isCritical = isCritical;
        text.life = CONFIG.UI.DAMAGE_TEXT_DURATION;
        text.maxLife = CONFIG.UI.DAMAGE_TEXT_DURATION;
        text.active = true;
        return text;
    }
    
    update(deltaTime) {
        const active = this.pool.active;
        for (let i = active.length - 1; i >= 0; i--) {
            const text = active[i];
            if (!text.active) continue;
            
            text.life -= deltaTime;
            text.y -= CONFIG.UI.DAMAGE_TEXT_RISE_SPEED * (deltaTime / 1000);
            
            if (text.life <= 0) {
                text.active = false;
                this.pool.release(text);
            }
        }
    }
    
    render(renderer) {
        this.pool.active.forEach(text => {
            if (!text.active) return;
            
            const alpha = text.life / text.maxLife;
            const color = text.isCritical ? '#ffff00' : '#ffffff';
            const fontSize = text.isCritical ? CONFIG.UI.FONT_SIZE_LARGE : CONFIG.UI.FONT_SIZE_MEDIUM;
            
            renderer.ctx.save();
            renderer.ctx.globalAlpha = alpha;
            renderer.drawWorldText(text.damage.toString(), text.x, text.y, color, fontSize);
            renderer.ctx.restore();
        });
    }
    
    getActiveCount() {
        return this.pool.getActiveCount();
    }
}

class ParticlePool {
    constructor() {
        this.pool = new ObjectPool(
            () => this.createParticle(),
            (obj) => this.resetParticle(obj),
            CONFIG.PERFORMANCE.MAX_PARTICLES
        );
    }
    
    createParticle() {
        return {
            x: 0,
            y: 0,
            velocityX: 0,
            velocityY: 0,
            life: 0,
            maxLife: 0,
            size: 1,
            color: '#ffffff',
            active: false,
            gravity: 0,
            friction: 1
        };
    }
    
    resetParticle(obj) {
        obj.x = 0;
        obj.y = 0;
        obj.velocityX = 0;
        obj.velocityY = 0;
        obj.life = 0;
        obj.maxLife = 0;
        obj.size = 1;
        obj.color = '#ffffff';
        obj.active = false;
        obj.gravity = 0;
        obj.friction = 1;
    }
    
    spawn(x, y, velocityX, velocityY, life, size = 2, color = '#ffffff', gravity = 0, friction = 0.98) {
        const particle = this.pool.get();
        particle.x = x;
        particle.y = y;
        particle.velocityX = velocityX;
        particle.velocityY = velocityY;
        particle.life = life;
        particle.maxLife = life;
        particle.size = size;
        particle.color = color;
        particle.active = true;
        particle.gravity = gravity;
        particle.friction = friction;
        return particle;
    }
    
    update(deltaTime) {
        const active = this.pool.active;
        const dt = deltaTime / 1000;
        
        for (let i = active.length - 1; i >= 0; i--) {
            const particle = active[i];
            if (!particle.active) continue;
            
            // Update physics
            particle.velocityY += particle.gravity * dt;
            particle.velocityX *= particle.friction;
            particle.velocityY *= particle.friction;
            
            particle.x += particle.velocityX * dt;
            particle.y += particle.velocityY * dt;
            
            // Update life
            particle.life -= deltaTime;
            
            if (particle.life <= 0) {
                particle.active = false;
                this.pool.release(particle);
            }
        }
    }
    
    render(renderer) {
        this.pool.active.forEach(particle => {
            if (!particle.active) return;
            
            const alpha = particle.life / particle.maxLife;
            const size = particle.size * alpha;
            
            renderer.ctx.save();
            renderer.ctx.globalAlpha = alpha;
            renderer.ctx.fillStyle = particle.color;
            renderer.ctx.beginPath();
            renderer.ctx.arc(
                particle.x - renderer.camera.x,
                particle.y - renderer.camera.y,
                size,
                0,
                Math.PI * 2
            );
            renderer.ctx.fill();
            renderer.ctx.restore();
        });
    }
    
    // Convenience methods for common particle effects
    createBloodSplatter(x, y, count = 5) {
        for (let i = 0; i < count; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Utils.random(20, 60);
            const velocityX = Math.cos(angle) * speed;
            const velocityY = Math.sin(angle) * speed;
            
            this.spawn(
                x + Utils.random(-5, 5),
                y + Utils.random(-5, 5),
                velocityX,
                velocityY,
                Utils.random(500, 1000),
                Utils.random(1, 3),
                '#cc0000',
                50,
                0.95
            );
        }
    }
    
    createSparkles(x, y, count = 8) {
        for (let i = 0; i < count; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Utils.random(30, 80);
            const velocityX = Math.cos(angle) * speed;
            const velocityY = Math.sin(angle) * speed;
            
            this.spawn(
                x,
                y,
                velocityX,
                velocityY,
                Utils.random(800, 1200),
                Utils.random(1, 2),
                '#ffff00',
                0,
                0.98
            );
        }
    }
    
    createDustCloud(x, y, count = 6) {
        for (let i = 0; i < count; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Utils.random(10, 30);
            const velocityX = Math.cos(angle) * speed;
            const velocityY = Math.sin(angle) * speed - 20; // Slight upward bias
            
            this.spawn(
                x + Utils.random(-8, 8),
                y + Utils.random(-8, 8),
                velocityX,
                velocityY,
                Utils.random(1000, 1500),
                Utils.random(2, 4),
                '#888888',
                -10, // Negative gravity (floats up)
                0.99
            );
        }
    }
    
    getActiveCount() {
        return this.pool.getActiveCount();
    }
}

// Global pool instances
window.damageTextPool = new DamageTextPool();
window.particlePool = new ParticlePool();

// Export classes
window.ObjectPool = ObjectPool;
window.DamageTextPool = DamageTextPool;
window.ParticlePool = ParticlePool;
