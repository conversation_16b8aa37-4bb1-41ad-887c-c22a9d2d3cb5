/**
 * SPRITE ANIMATION SYSTEM
 * 
 * Frame-based animation system with state management for idle/walking/attacking.
 * Supports sprite sheets and individual frame animations.
 */

class SpriteAnimation {
    constructor(spriteSheet, frameWidth, frameHeight, animations = {}) {
        this.spriteSheet = spriteSheet;
        this.frameWidth = frameWidth;
        this.frameHeight = frameHeight;
        this.animations = animations;
        
        // Current animation state
        this.currentAnimation = null;
        this.currentFrame = 0;
        this.frameTime = 0;
        this.playing = false;
        this.loop = true;
        this.onComplete = null;
        
        // Default animation if none specified
        if (Object.keys(animations).length === 0) {
            this.animations = {
                idle: { frames: [0], frameRate: 1 }
            };
        }
        
        // Start with first animation
        const firstAnimation = Object.keys(this.animations)[0];
        this.play(firstAnimation);
    }
    
    play(animationName, loop = true, onComplete = null) {
        if (!this.animations[animationName]) {
            console.warn(`Animation '${animationName}' not found`);
            return;
        }
        
        // Only restart if it's a different animation or if it's not looping
        if (this.currentAnimation !== animationName || !this.playing) {
            this.currentAnimation = animationName;
            this.currentFrame = 0;
            this.frameTime = 0;
            this.playing = true;
            this.loop = loop;
            this.onComplete = onComplete;
        }
    }
    
    stop() {
        this.playing = false;
    }
    
    pause() {
        this.playing = false;
    }
    
    resume() {
        this.playing = true;
    }
    
    update(deltaTime) {
        if (!this.playing || !this.currentAnimation) return;
        
        const animation = this.animations[this.currentAnimation];
        if (!animation) return;
        
        this.frameTime += deltaTime;
        const frameDuration = 1000 / (animation.frameRate || 8); // Default 8 FPS
        
        if (this.frameTime >= frameDuration) {
            this.frameTime = 0;
            this.currentFrame++;
            
            if (this.currentFrame >= animation.frames.length) {
                if (this.loop) {
                    this.currentFrame = 0;
                } else {
                    this.currentFrame = animation.frames.length - 1;
                    this.playing = false;
                    if (this.onComplete) {
                        this.onComplete();
                    }
                }
            }
        }
    }
    
    getCurrentFrame() {
        if (!this.currentAnimation) return 0;
        
        const animation = this.animations[this.currentAnimation];
        if (!animation || !animation.frames) return 0;
        
        return animation.frames[this.currentFrame] || 0;
    }
    
    render(renderer, x, y, width, height) {
        if (!this.spriteSheet) return;
        
        const frameIndex = this.getCurrentFrame();
        const framesPerRow = Math.floor(this.spriteSheet.width / this.frameWidth);
        
        const sourceX = (frameIndex % framesPerRow) * this.frameWidth;
        const sourceY = Math.floor(frameIndex / framesPerRow) * this.frameHeight;
        
        renderer.ctx.drawImage(
            this.spriteSheet,
            sourceX, sourceY, this.frameWidth, this.frameHeight,
            x - renderer.camera.x, y - renderer.camera.y, width, height
        );
    }
    
    isPlaying() {
        return this.playing;
    }
    
    getCurrentAnimationName() {
        return this.currentAnimation;
    }
    
    hasAnimation(name) {
        return !!this.animations[name];
    }
    
    addAnimation(name, frames, frameRate = 8) {
        this.animations[name] = { frames, frameRate };
    }
    
    removeAnimation(name) {
        delete this.animations[name];
    }
    
    // Get animation progress (0-1)
    getProgress() {
        if (!this.currentAnimation) return 0;
        
        const animation = this.animations[this.currentAnimation];
        if (!animation || !animation.frames) return 0;
        
        return this.currentFrame / animation.frames.length;
    }
}

class AnimatedSprite {
    constructor(assetLoader, spriteName) {
        this.assetLoader = assetLoader;
        this.spriteName = spriteName;
        this.animation = null;
        this.loaded = false;
        
        this.init();
    }
    
    async init() {
        const spriteConfig = this.assetLoader.getSpriteConfig(this.spriteName);
        const spriteSheet = this.assetLoader.getSprite(this.spriteName);
        
        if (spriteConfig && spriteSheet) {
            const animations = this.createDefaultAnimations(spriteConfig);
            this.animation = new SpriteAnimation(
                spriteSheet,
                spriteConfig.frameWidth || CONFIG.GAME.TILE_SIZE,
                spriteConfig.frameHeight || CONFIG.GAME.TILE_SIZE,
                animations
            );
            this.loaded = true;
        }
    }
    
    createDefaultAnimations(config) {
        const frameCount = config.frames || 1;
        
        if (frameCount === 1) {
            return {
                idle: { frames: [0], frameRate: 1 }
            };
        } else if (frameCount === 4) {
            // Standard 4-frame character animation
            return {
                idle: { frames: [0], frameRate: 1 },
                walk: { frames: [0, 1, 2, 3], frameRate: 8 },
                attack: { frames: [0, 1, 2, 3], frameRate: 12 }
            };
        } else {
            // Generic animation for any frame count
            const frames = Array.from({ length: frameCount }, (_, i) => i);
            return {
                idle: { frames: [0], frameRate: 1 },
                animate: { frames, frameRate: 8 }
            };
        }
    }
    
    update(deltaTime) {
        if (this.animation) {
            this.animation.update(deltaTime);
        }
    }
    
    render(renderer, x, y, width, height) {
        if (this.animation) {
            this.animation.render(renderer, x, y, width, height);
        } else {
            // Fallback to static sprite
            const sprite = this.assetLoader.getSprite(this.spriteName);
            renderer.ctx.drawImage(
                sprite,
                x - renderer.camera.x,
                y - renderer.camera.y,
                width,
                height
            );
        }
    }
    
    play(animationName, loop = true, onComplete = null) {
        if (this.animation) {
            this.animation.play(animationName, loop, onComplete);
        }
    }
    
    stop() {
        if (this.animation) {
            this.animation.stop();
        }
    }
    
    isLoaded() {
        return this.loaded;
    }
    
    hasAnimation(name) {
        return this.animation ? this.animation.hasAnimation(name) : false;
    }
    
    getCurrentAnimation() {
        return this.animation ? this.animation.getCurrentAnimationName() : null;
    }
}

// Animation state manager for entities
class AnimationStateMachine {
    constructor(animatedSprite) {
        this.animatedSprite = animatedSprite;
        this.currentState = 'idle';
        this.previousState = null;
        this.stateTransitions = {};
    }
    
    addTransition(fromState, toState, condition) {
        if (!this.stateTransitions[fromState]) {
            this.stateTransitions[fromState] = [];
        }
        this.stateTransitions[fromState].push({ toState, condition });
    }
    
    update(entity, deltaTime) {
        // Check for state transitions
        const transitions = this.stateTransitions[this.currentState] || [];
        
        for (const transition of transitions) {
            if (transition.condition(entity)) {
                this.setState(transition.toState);
                break;
            }
        }
        
        // Update animation
        if (this.animatedSprite) {
            this.animatedSprite.update(deltaTime);
        }
    }
    
    setState(newState) {
        if (newState !== this.currentState) {
            this.previousState = this.currentState;
            this.currentState = newState;
            
            if (this.animatedSprite && this.animatedSprite.hasAnimation(newState)) {
                this.animatedSprite.play(newState);
            }
        }
    }
    
    getCurrentState() {
        return this.currentState;
    }
    
    getPreviousState() {
        return this.previousState;
    }
}

// Export classes
window.SpriteAnimation = SpriteAnimation;
window.AnimatedSprite = AnimatedSprite;
window.AnimationStateMachine = AnimationStateMachine;
