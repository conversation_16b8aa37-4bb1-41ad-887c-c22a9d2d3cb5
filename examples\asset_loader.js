/**
 * EXAMPLE: AssetLoader Class for Texture Management
 * 
 * Add this as a new file: js/asset_loader.js
 */

class AssetLoader {
    constructor() {
        this.loadedAssets = new Map();
        this.loadingPromises = new Map();
        this.manifest = null;
    }
    
    async loadManifest(manifestPath) {
        try {
            const response = await fetch(manifestPath);
            this.manifest = await response.json();
            console.log('Asset manifest loaded:', this.manifest);
        } catch (error) {
            console.error('Failed to load asset manifest:', error);
            this.manifest = this.getDefaultManifest();
        }
    }
    
    getDefaultManifest() {
        return {
            sprites: {
                player: { path: 'assets/sprites/player.png', frames: 4 },
                goblin: { path: 'assets/sprites/goblin.png', frames: 4 },
                skeleton: { path: 'assets/sprites/skeleton.png', frames: 4 },
                orc: { path: 'assets/sprites/orc.png', frames: 4 },
                wall: { path: 'assets/tiles/wall.png', frames: 1 },
                floor: { path: 'assets/tiles/floor.png', frames: 1 },
                sword: { path: 'assets/items/sword.png', frames: 1 },
                armor: { path: 'assets/items/armor.png', frames: 1 },
                potion: { path: 'assets/items/potion.png', frames: 1 }
            },
            sounds: {
                attack: 'assets/sounds/attack.wav',
                hit: 'assets/sounds/hit.wav',
                pickup: 'assets/sounds/pickup.wav'
            }
        };
    }
    
    async loadImage(path) {
        if (this.loadedAssets.has(path)) {
            return this.loadedAssets.get(path);
        }
        
        if (this.loadingPromises.has(path)) {
            return this.loadingPromises.get(path);
        }
        
        const promise = new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.loadedAssets.set(path, img);
                this.loadingPromises.delete(path);
                resolve(img);
            };
            img.onerror = () => {
                this.loadingPromises.delete(path);
                console.warn(`Failed to load image: ${path}, using fallback`);
                resolve(this.createFallbackSprite());
            };
            img.src = path;
        });
        
        this.loadingPromises.set(path, promise);
        return promise;
    }
    
    createFallbackSprite() {
        const canvas = document.createElement('canvas');
        canvas.width = CONFIG.GAME.TILE_SIZE;
        canvas.height = CONFIG.GAME.TILE_SIZE;
        const ctx = canvas.getContext('2d');
        
        ctx.fillStyle = '#ff00ff'; // Magenta for missing textures
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.strokeStyle = '#000000';
        ctx.strokeRect(0, 0, canvas.width, canvas.height);
        
        return canvas;
    }
    
    async loadAllSprites() {
        if (!this.manifest) {
            await this.loadManifest('assets/manifest.json');
        }
        
        const loadPromises = [];
        for (const [name, config] of Object.entries(this.manifest.sprites)) {
            loadPromises.push(this.loadImage(config.path));
        }
        
        await Promise.all(loadPromises);
        console.log('All sprites loaded successfully');
    }
    
    getSprite(name) {
        const config = this.manifest?.sprites[name];
        if (!config) {
            console.warn(`Unknown sprite: ${name}`);
            return this.createFallbackSprite();
        }
        
        return this.loadedAssets.get(config.path) || this.createFallbackSprite();
    }
}

// Usage in renderer.js:
// Replace createPlaceholderSprites() with:
// async loadSprites() {
//     this.assetLoader = new AssetLoader();
//     await this.assetLoader.loadAllSprites();
//     
//     // Update sprite cache with real images
//     for (const spriteName of ['player', 'goblin', 'skeleton', 'orc', 'wall', 'floor', 'sword', 'armor', 'potion']) {
//         this.spriteCache.set(spriteName, this.assetLoader.getSprite(spriteName));
//     }
// }

window.AssetLoader = AssetLoader;
