/**
 * TEXTURE ATLAS SYSTEM
 * 
 * Efficient sprite sheet parsing and management system.
 * Supports packed texture atlases and uniform grid layouts.
 */

class TextureAtlas {
    constructor(image, atlasData) {
        this.image = image;
        this.atlasData = atlasData;
        this.sprites = new Map();
        this.parseAtlas();
    }
    
    parseAtlas() {
        if (this.atlasData.type === 'grid') {
            this.parseGridAtlas();
        } else if (this.atlasData.type === 'packed') {
            this.parsePackedAtlas();
        } else {
            console.warn('Unknown atlas type:', this.atlasData.type);
        }
    }
    
    parseGridAtlas() {
        const { tileWidth, tileHeight, columns, rows, sprites } = this.atlasData;
        
        for (const [name, config] of Object.entries(sprites)) {
            const index = config.index || 0;
            const x = (index % columns) * tileWidth;
            const y = Math.floor(index / columns) * tileHeight;
            
            this.sprites.set(name, {
                x: x,
                y: y,
                width: tileWidth,
                height: tileHeight,
                frames: config.frames || 1,
                frameWidth: tileWidth,
                frameHeight: tileHeight
            });
        }
    }
    
    parsePackedAtlas() {
        // For packed atlases (like TexturePacker output)
        const { sprites } = this.atlasData;
        
        for (const [name, config] of Object.entries(sprites)) {
            this.sprites.set(name, {
                x: config.x,
                y: config.y,
                width: config.width,
                height: config.height,
                frames: config.frames || 1,
                frameWidth: config.frameWidth || config.width,
                frameHeight: config.frameHeight || config.height
            });
        }
    }
    
    getSprite(name) {
        return this.sprites.get(name);
    }
    
    hasSprite(name) {
        return this.sprites.has(name);
    }
    
    drawSprite(ctx, name, destX, destY, destWidth, destHeight, frame = 0) {
        const sprite = this.sprites.get(name);
        if (!sprite) {
            console.warn(`Sprite '${name}' not found in atlas`);
            return false;
        }
        
        // Calculate frame position for multi-frame sprites
        let sourceX = sprite.x;
        let sourceY = sprite.y;
        
        if (sprite.frames > 1) {
            const framesPerRow = Math.floor(sprite.width / sprite.frameWidth);
            const frameX = frame % framesPerRow;
            const frameY = Math.floor(frame / framesPerRow);
            
            sourceX += frameX * sprite.frameWidth;
            sourceY += frameY * sprite.frameHeight;
        }
        
        ctx.drawImage(
            this.image,
            sourceX, sourceY, sprite.frameWidth, sprite.frameHeight,
            destX, destY, destWidth, destHeight
        );
        
        return true;
    }
    
    createSpriteCanvas(name, frame = 0) {
        const sprite = this.sprites.get(name);
        if (!sprite) return null;
        
        const canvas = document.createElement('canvas');
        canvas.width = sprite.frameWidth;
        canvas.height = sprite.frameHeight;
        const ctx = canvas.getContext('2d');
        
        this.drawSprite(ctx, name, 0, 0, sprite.frameWidth, sprite.frameHeight, frame);
        
        return canvas;
    }
    
    getAllSpriteNames() {
        return Array.from(this.sprites.keys());
    }
    
    getSpriteInfo(name) {
        return this.sprites.get(name);
    }
}

class AtlasManager {
    constructor() {
        this.atlases = new Map();
        this.loadingPromises = new Map();
    }
    
    async loadAtlas(name, imagePath, atlasDataPath) {
        if (this.atlases.has(name)) {
            return this.atlases.get(name);
        }
        
        if (this.loadingPromises.has(name)) {
            return this.loadingPromises.get(name);
        }
        
        const promise = this.loadAtlasInternal(name, imagePath, atlasDataPath);
        this.loadingPromises.set(name, promise);
        
        try {
            const atlas = await promise;
            this.atlases.set(name, atlas);
            this.loadingPromises.delete(name);
            return atlas;
        } catch (error) {
            this.loadingPromises.delete(name);
            throw error;
        }
    }
    
    async loadAtlasInternal(name, imagePath, atlasDataPath) {
        try {
            // Load image and atlas data in parallel
            const [image, atlasResponse] = await Promise.all([
                this.loadImage(imagePath),
                fetch(atlasDataPath)
            ]);
            
            if (!atlasResponse.ok) {
                throw new Error(`Failed to load atlas data: ${atlasResponse.status}`);
            }
            
            const atlasData = await atlasResponse.json();
            return new TextureAtlas(image, atlasData);
            
        } catch (error) {
            console.error(`Failed to load atlas '${name}':`, error);
            // Return a fallback atlas
            return this.createFallbackAtlas();
        }
    }
    
    loadImage(path) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`Failed to load image: ${path}`));
            img.src = path;
        });
    }
    
    createFallbackAtlas() {
        // Create a simple fallback atlas with basic sprites
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const ctx = canvas.getContext('2d');
        
        // Draw a simple grid pattern
        ctx.fillStyle = '#ff00ff';
        ctx.fillRect(0, 0, 64, 64);
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, 32, 32);
        ctx.fillRect(32, 32, 32, 32);
        
        const atlasData = {
            type: 'grid',
            tileWidth: 16,
            tileHeight: 16,
            columns: 4,
            rows: 4,
            sprites: {
                fallback: { index: 0, frames: 1 }
            }
        };
        
        return new TextureAtlas(canvas, atlasData);
    }
    
    getAtlas(name) {
        return this.atlases.get(name);
    }
    
    hasAtlas(name) {
        return this.atlases.has(name);
    }
    
    drawSprite(ctx, atlasName, spriteName, x, y, width, height, frame = 0) {
        const atlas = this.atlases.get(atlasName);
        if (!atlas) {
            console.warn(`Atlas '${atlasName}' not found`);
            return false;
        }
        
        return atlas.drawSprite(ctx, spriteName, x, y, width, height, frame);
    }
    
    // Create a default game atlas configuration
    createDefaultGameAtlas() {
        return {
            type: 'grid',
            tileWidth: 16,
            tileHeight: 16,
            columns: 8,
            rows: 8,
            sprites: {
                player: { index: 0, frames: 4 },
                goblin: { index: 4, frames: 4 },
                skeleton: { index: 8, frames: 4 },
                orc: { index: 12, frames: 4 },
                wall: { index: 16, frames: 1 },
                floor: { index: 17, frames: 1 },
                door: { index: 18, frames: 1 },
                sword: { index: 20, frames: 1 },
                armor: { index: 21, frames: 1 },
                potion: { index: 22, frames: 1 }
            }
        };
    }
    
    // Load the main game atlas
    async loadGameAtlas() {
        try {
            await this.loadAtlas('game', 'assets/sprites/game_atlas.png', 'assets/sprites/game_atlas.json');
        } catch (error) {
            console.warn('Failed to load game atlas, creating fallback');
            // Create a fallback atlas with the default configuration
            const fallbackAtlas = this.createFallbackAtlas();
            this.atlases.set('game', fallbackAtlas);
        }
    }
    
    // Clear all loaded atlases
    clear() {
        this.atlases.clear();
        this.loadingPromises.clear();
    }
}

// Global atlas manager instance
window.atlasManager = new AtlasManager();

// Export classes
window.TextureAtlas = TextureAtlas;
window.AtlasManager = AtlasManager;
