/**
 * DUNGEON FEATURES CLASS
 * 
 * Handles interactive dungeon elements like traps, treasure chests, doors, etc.
 */

class DungeonFeatures {
    constructor(game) {
        this.game = game;
        this.features = new Map(); // Map of position keys to feature objects
        this.activatedTraps = new Set(); // Track activated traps
        this.openedChests = new Set(); // Track opened chests
        this.unlockedDoors = new Set(); // Track unlocked doors
    }

    // Add a feature at a specific tile position
    addFeature(tileX, tileY, type, data = {}) {
        const key = `${tileX},${tileY}`;
        this.features.set(key, {
            x: tileX,
            y: tileY,
            type: type,
            data: data,
            active: true
        });
    }

    // Get feature at tile position
    getFeature(tileX, tileY) {
        const key = `${tileX},${tileY}`;
        return this.features.get(key);
    }

    // Handle player interaction with features
    handlePlayerInteraction(player) {
        const tileSize = CONFIG.GAME.SCALED_TILE_SIZE;
        const playerTileX = Math.floor((player.x + player.width / 2) / tileSize);
        const playerTileY = Math.floor((player.y + player.height / 2) / tileSize);

        const feature = this.getFeature(playerTileX, playerTileY);
        if (!feature || !feature.active) return;

        switch (feature.type) {
            case 'trap':
                this.activateTrap(feature, player);
                break;
            case 'treasure_chest':
                if (inputManager.isInteracting()) {
                    this.openTreasureChest(feature, player);
                }
                break;
            case 'locked_door':
                if (inputManager.isInteracting()) {
                    this.tryUnlockDoor(feature, player);
                }
                break;
            case 'secret_wall':
                if (inputManager.isInteracting()) {
                    this.revealSecretWall(feature);
                }
                break;
        }
    }

    activateTrap(feature, player) {
        const key = `${feature.x},${feature.y}`;
        if (this.activatedTraps.has(key)) return;

        this.activatedTraps.add(key);
        feature.active = false;

        // Determine trap type and apply effects
        const trapType = feature.data.trapType || 'spike';
        
        switch (trapType) {
            case 'spike':
                this.activateSpikeTrap(feature, player);
                break;
            case 'poison':
                this.activatePoisonTrap(feature, player);
                break;
            case 'explosion':
                this.activateExplosionTrap(feature, player);
                break;
        }

        // Play trap sound
        if (window.soundManager) {
            window.soundManager.playTrapActivated();
        }

        console.log(`Trap activated at (${feature.x}, ${feature.y})`);
    }

    activateSpikeTrap(feature, player) {
        const damage = CONFIG.DUNGEON.SPIKE_TRAP_DAMAGE || 15;
        player.takeDamage(damage);

        // Visual effect
        if (window.particlePool) {
            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            
            // Spike particles
            for (let i = 0; i < 8; i++) {
                window.particlePool.spawn(
                    worldX,
                    worldY,
                    Utils.random(-50, 50),
                    Utils.random(-80, -20),
                    800,
                    3,
                    '#8b4513',
                    0,
                    0.95
                );
            }
        }

        // Damage text
        if (window.renderer) {
            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE;
            window.renderer.addDamageText(worldX, worldY, damage.toString(), false);
        }
    }

    activatePoisonTrap(feature, player) {
        const damage = CONFIG.DUNGEON.POISON_TRAP_DAMAGE || 8;
        const duration = CONFIG.DUNGEON.POISON_DURATION || 5000;
        
        // Apply poison status effect
        if (player.addStatusEffect) {
            player.addStatusEffect('poison', duration, damage);
        } else {
            // Fallback: immediate damage
            player.takeDamage(damage);
        }

        // Visual effect
        if (window.particlePool) {
            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            
            // Poison cloud
            for (let i = 0; i < 12; i++) {
                window.particlePool.spawn(
                    worldX,
                    worldY,
                    Utils.random(-30, 30),
                    Utils.random(-40, -10),
                    2000,
                    4,
                    '#00ff00',
                    0,
                    0.98
                );
            }
        }
    }

    activateExplosionTrap(feature, player) {
        const damage = 25;
        player.takeDamage(damage);

        // Explosion effect
        if (window.particlePool) {
            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            
            // Explosion particles
            for (let i = 0; i < 20; i++) {
                window.particlePool.spawn(
                    worldX,
                    worldY,
                    Utils.random(-100, 100),
                    Utils.random(-100, 100),
                    1000,
                    5,
                    Utils.randomChoice(['#ff4500', '#ff6600', '#ffff00']),
                    0,
                    0.92
                );
            }
        }

        // Screen shake effect
        if (window.renderer) {
            window.renderer.addScreenShake(300, 8);
        }
    }

    openTreasureChest(feature, player) {
        const key = `${feature.x},${feature.y}`;
        if (this.openedChests.has(key)) return;

        this.openedChests.add(key);
        feature.active = false;

        // Generate treasure
        const itemCount = Utils.random(
            CONFIG.DUNGEON.CHEST_ITEM_COUNT_MIN || 1,
            CONFIG.DUNGEON.CHEST_ITEM_COUNT_MAX || 3
        );

        const items = [];
        for (let i = 0; i < itemCount; i++) {
            // Higher chance for rare items in chests
            const itemInfo = this.game.getRandomItem();
            if (Math.random() < (CONFIG.DUNGEON.CHEST_RARE_ITEM_CHANCE || 0.3)) {
                // Force rare or better
                itemInfo.rarity = Utils.randomChoice(['RARE', 'EPIC', 'LEGENDARY']);
            }
            items.push(itemInfo);
        }

        // Add items to player inventory
        items.forEach(item => {
            if (player.inventory && player.inventory[item.category] !== undefined) {
                player.inventory[item.category]++;
            }
        });

        // Visual effect
        if (window.particlePool) {
            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            
            // Treasure sparkles
            for (let i = 0; i < 15; i++) {
                window.particlePool.spawn(
                    worldX,
                    worldY,
                    Utils.random(-40, 40),
                    Utils.random(-60, -20),
                    1500,
                    3,
                    '#ffd700',
                    0,
                    0.96
                );
            }
        }

        // Play treasure sound
        if (window.soundManager) {
            window.soundManager.playTreasureFound();
        }

        // Show treasure text
        if (window.renderer) {
            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE;
            window.renderer.addDamageText(worldX, worldY, 'TREASURE!', true);
        }

        console.log(`Treasure chest opened! Found ${itemCount} items.`);
    }

    tryUnlockDoor(feature, player) {
        // Check if player has a key (simplified)
        if (player.inventory && player.inventory.consumables > 0) {
            this.unlockDoor(feature, player);
            player.inventory.consumables--; // Use a key
        } else {
            // Show "need key" message
            if (window.renderer) {
                const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
                const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE;
                window.renderer.addDamageText(worldX, worldY, 'NEED KEY', false);
            }
        }
    }

    unlockDoor(feature, player) {
        const key = `${feature.x},${feature.y}`;
        if (this.unlockedDoors.has(key)) return;

        this.unlockedDoors.add(key);
        
        // Change tile type to regular door
        if (this.game.dungeon) {
            this.game.dungeon.setTile(feature.x, feature.y, CONFIG.DUNGEON.TILE_TYPES.DOOR);
        }

        // Play unlock sound
        if (window.soundManager) {
            window.soundManager.playDoorUnlock();
        }

        // Visual effect
        if (window.particlePool) {
            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            
            // Unlock sparkles
            for (let i = 0; i < 8; i++) {
                window.particlePool.spawn(
                    worldX,
                    worldY,
                    Utils.random(-20, 20),
                    Utils.random(-30, -10),
                    1000,
                    2,
                    '#ffd700',
                    0,
                    0.95
                );
            }
        }

        console.log(`Door unlocked at (${feature.x}, ${feature.y})`);
    }

    revealSecretWall(feature) {
        // Change tile type to floor
        if (this.game.dungeon) {
            this.game.dungeon.setTile(feature.x, feature.y, CONFIG.DUNGEON.TILE_TYPES.FLOOR);
        }

        feature.active = false;

        // Play secret sound
        if (window.soundManager) {
            window.soundManager.playSecretFound();
        }

        // Visual effect
        if (window.particlePool) {
            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE + CONFIG.GAME.SCALED_TILE_SIZE / 2;
            
            // Dust particles
            for (let i = 0; i < 10; i++) {
                window.particlePool.spawn(
                    worldX,
                    worldY,
                    Utils.random(-30, 30),
                    Utils.random(-40, -10),
                    1200,
                    3,
                    '#cccccc',
                    0,
                    0.94
                );
            }
        }

        console.log(`Secret wall revealed at (${feature.x}, ${feature.y})`);
    }

    // Update method for any time-based features
    update(deltaTime) {
        // Could add animated features, timed traps, etc.
    }

    // Render any special effects or indicators
    render(renderer) {
        // Could render interaction prompts, trap indicators, etc.
        for (const [key, feature] of this.features.entries()) {
            if (!feature.active) continue;

            const worldX = feature.x * CONFIG.GAME.SCALED_TILE_SIZE;
            const worldY = feature.y * CONFIG.GAME.SCALED_TILE_SIZE;

            // Render interaction prompts for chests and doors
            if ((feature.type === 'treasure_chest' || feature.type === 'locked_door') && 
                this.isPlayerNear(feature)) {
                renderer.drawText('E', worldX + 8, worldY - 5, '#ffff00', 12);
            }
        }
    }

    isPlayerNear(feature) {
        if (!this.game.player) return false;
        
        const tileSize = CONFIG.GAME.SCALED_TILE_SIZE;
        const playerTileX = Math.floor((this.game.player.x + this.game.player.width / 2) / tileSize);
        const playerTileY = Math.floor((this.game.player.y + this.game.player.height / 2) / tileSize);
        
        const distance = Math.abs(playerTileX - feature.x) + Math.abs(playerTileY - feature.y);
        return distance <= 1;
    }
}

// Export for use in other modules
window.DungeonFeatures = DungeonFeatures;
