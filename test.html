<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dungeon Crawler - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        iframe {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🎮 Dungeon Crawler - Game Test</h1>
    
    <div class="test-section">
        <h2>Game Status</h2>
        <div id="gameStatus" class="status info">Loading game...</div>
        <button onclick="runTests()">Run Tests</button>
        <button onclick="openGame()">Open Full Game</button>
    </div>
    
    <div class="test-section">
        <h2>Game Preview</h2>
        <iframe src="index.html" id="gameFrame"></iframe>
    </div>
    
    <div class="test-section">
        <h2>Features Implemented ✅</h2>
        <ul>
            <li><strong>✅ Project Setup</strong> - HTML5/JavaScript structure with comprehensive config system</li>
            <li><strong>✅ Core Game Engine</strong> - Game loop, canvas setup, input handling, systems architecture</li>
            <li><strong>✅ Player Character</strong> - Movement, stats, rendering with 16x16 sprites</li>
            <li><strong>✅ Combat System</strong> - Real-time action combat with mouse/touch targeting</li>
            <li><strong>✅ Dungeon Generation</strong> - Procedural rectangular rooms with corridors</li>
            <li><strong>✅ Enemy AI</strong> - 3 enemy types (Goblin, Skeleton, Orc) with different behaviors</li>
            <li><strong>✅ Item System</strong> - 10+ item types with stats and inventory tracking</li>
            <li><strong>✅ Mobile Controls</strong> - Touch-friendly controls and responsive UI</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Game Controls</h2>
        <div class="info">
            <h4>Desktop Controls:</h4>
            <ul>
                <li><strong>WASD / Arrow Keys:</strong> Move player</li>
                <li><strong>Mouse:</strong> Aim attack direction</li>
                <li><strong>Left Click / Space:</strong> Attack enemies</li>
                <li><strong>E:</strong> Interact</li>
                <li><strong>I / Tab:</strong> Inventory (debug info)</li>
                <li><strong>Escape:</strong> Pause</li>
            </ul>
            
            <h4>Mobile Controls:</h4>
            <ul>
                <li><strong>Touch Left Side:</strong> Virtual joystick for movement</li>
                <li><strong>Touch Right Side:</strong> Attack</li>
                <li><strong>Attack Button:</strong> Alternative attack control</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Configuration System</h2>
        <div class="info">
            <p>All gameplay variables are centralized in <code>js/config.js</code> for easy balancing:</p>
            <ul>
                <li><strong>Player Stats:</strong> Health, attack, defense, speed, leveling</li>
                <li><strong>Enemy Properties:</strong> Health, damage, AI behavior for each type</li>
                <li><strong>Combat Settings:</strong> Damage variance, critical hits, defense</li>
                <li><strong>Dungeon Generation:</strong> Room sizes, spawn rates, corridors</li>
                <li><strong>UI Settings:</strong> Mobile responsiveness, scaling, controls</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Technical Specifications</h2>
        <div class="success">
            <ul>
                <li><strong>Technology:</strong> HTML5 Canvas + JavaScript (as requested, not Bevy/Rust)</li>
                <li><strong>Art Style:</strong> 16x16 pixel tiles with 2x scaling</li>
                <li><strong>Combat:</strong> Real-time action with mouse/touch direction targeting</li>
                <li><strong>Progression:</strong> Simplified RPG stats (Attack, Defense, Health, Speed)</li>
                <li><strong>Platform:</strong> Web and mobile optimized for .io games market</li>
                <li><strong>Content:</strong> 3 enemy types, 10+ item types for MVP</li>
            </ul>
        </div>
    </div>

    <script>
        function runTests() {
            const status = document.getElementById('gameStatus');
            status.className = 'status info';
            status.innerHTML = 'Running tests...';
            
            setTimeout(() => {
                // Simple tests
                const tests = [
                    { name: 'Config loaded', check: () => typeof CONFIG !== 'undefined' },
                    { name: 'Utils available', check: () => typeof Utils !== 'undefined' },
                    { name: 'Canvas element exists', check: () => document.getElementById('gameCanvas') !== null },
                    { name: 'Game files loaded', check: () => typeof Entity !== 'undefined' && typeof Player !== 'undefined' }
                ];
                
                let passed = 0;
                let results = '<h4>Test Results:</h4><ul>';
                
                tests.forEach(test => {
                    try {
                        if (test.check()) {
                            results += `<li>✅ ${test.name}</li>`;
                            passed++;
                        } else {
                            results += `<li>❌ ${test.name}</li>`;
                        }
                    } catch (e) {
                        results += `<li>❌ ${test.name} (Error: ${e.message})</li>`;
                    }
                });
                
                results += '</ul>';
                
                if (passed === tests.length) {
                    status.className = 'status success';
                    status.innerHTML = `All tests passed! (${passed}/${tests.length})` + results;
                } else {
                    status.className = 'status error';
                    status.innerHTML = `Some tests failed (${passed}/${tests.length})` + results;
                }
            }, 1000);
        }
        
        function openGame() {
            window.open('index.html', '_blank');
        }
        
        // Auto-run tests on load
        window.addEventListener('load', () => {
            setTimeout(runTests, 2000);
        });
    </script>
</body>
</html>
