/**
 * COMBAT SYSTEM
 * 
 * Handles combat interactions between player and enemies.
 */

class CombatSystem {
    constructor() {
        this.activeAttacks = [];
        this.attackEffects = []; // Visual attack effects
    }

    update(deltaTime, player, enemies) {
        // Handle player attacks
        if (player && player.currentAttack) {
            console.log('Processing player attack!'); // Debug log
            this.handlePlayerAttack(player, enemies);

            // Add visual attack effect
            this.addAttackEffect(player.currentAttack);

            player.currentAttack = null;
        }

        // Handle enemy attacks
        enemies.forEach(enemy => {
            if (enemy.currentAttack) {
                this.handleEnemyAttack(enemy, player);
                enemy.currentAttack = null;
            }
        });

        // Update attack effects
        this.updateAttackEffects(deltaTime);
    }
    
    handlePlayerAttack(player, enemies) {
        const attack = player.currentAttack;
        let hitCount = 0;

        // Use spatial grid for more efficient collision detection if available
        let enemiesToCheck = enemies;
        if (window.game && window.game.spatialGrid) {
            enemiesToCheck = window.game.spatialGrid.queryRadius(
                attack.x,
                attack.y,
                attack.range
            ).filter(entity => entity.type === 'enemy');
        }

        enemiesToCheck.forEach(enemy => {
            if (this.isInAttackRange(attack, enemy)) {
                console.log(`Player hit ${enemy.enemyType}!`); // Debug log
                enemy.takeDamage(attack.damage, player);
                hitCount++;

                // Play hit sound effect
                if (window.soundManager) {
                    window.soundManager.playEnemyHit();
                }

                // Add blood splatter effect
                if (window.particlePool) {
                    window.particlePool.createBloodSplatter(
                        enemy.x + enemy.width / 2,
                        enemy.y + enemy.height / 2
                    );
                }
            }
        });

        console.log(`Player attack hit ${hitCount} enemies`); // Debug log
    }
    
    handleEnemyAttack(enemy, player) {
        const attack = enemy.currentAttack;
        
        if (attack.target === player) {
            const distance = enemy.distanceTo(player);
            if (distance <= attack.range) {
                player.takeDamage(attack.damage, enemy);
            }
        }
    }
    
    isInAttackRange(attack, target) {
        const targetCenter = target.getCenter();
        const distance = Utils.distance(attack.x, attack.y, targetCenter.x, targetCenter.y);
        const inRange = distance <= attack.range;

        if (CONFIG.GAME.DEBUG_MODE) {
            console.log(`Attack range check: distance=${distance.toFixed(1)}, range=${attack.range}, hit=${inRange}`);
        }

        return inRange;
    }

    addAttackEffect(attack) {
        this.attackEffects.push({
            x: attack.x,
            y: attack.y,
            range: attack.range,
            life: 200, // Effect duration in milliseconds
            maxLife: 200
        });
    }

    updateAttackEffects(deltaTime) {
        for (let i = this.attackEffects.length - 1; i >= 0; i--) {
            const effect = this.attackEffects[i];
            effect.life -= deltaTime;

            if (effect.life <= 0) {
                this.attackEffects.splice(i, 1);
            }
        }
    }

    renderAttackEffects(renderer) {
        this.attackEffects.forEach(effect => {
            const alpha = effect.life / effect.maxLife;
            const radius = effect.range * (1 - alpha * 0.5); // Shrinking effect

            renderer.ctx.save();
            renderer.ctx.globalAlpha = alpha * 0.5;
            renderer.ctx.strokeStyle = '#ffff00';
            renderer.ctx.lineWidth = 3;
            renderer.ctx.beginPath();
            renderer.ctx.arc(
                effect.x - renderer.camera.x,
                effect.y - renderer.camera.y,
                radius,
                0,
                Math.PI * 2
            );
            renderer.ctx.stroke();
            renderer.ctx.restore();
        });
    }
}

window.CombatSystem = CombatSystem;
