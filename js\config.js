/**
 * DUNGEON CRAWLER GAME CONFIGURATION
 * 
 * This file contains ALL gameplay variables and settings for easy balancing.
 * Modify values here to adjust game difficulty, mechanics, and behavior
 * without changing core game code.
 * 
 * Value ranges and recommendations are provided in comments.
 */

const CONFIG = {
    
    // ========================================
    // GAME SETTINGS
    // ========================================
    GAME: {
        // Canvas and rendering settings
        CANVAS_WIDTH: 800,           // Game canvas width in pixels
        CANVAS_HEIGHT: 600,          // Game canvas height in pixels
        TARGET_FPS: 60,              // Target frames per second
        PIXEL_SCALE: 2,              // Pixel art scaling factor (1-4 recommended)
        
        // Tile system (all sprites must be 16x16 pixels)
        TILE_SIZE: 16,               // Base tile size in pixels
        SCALED_TILE_SIZE: 32,        // Tile size after scaling (TILE_SIZE * PIXEL_SCALE)
        
        // Debug settings
        DEBUG_MODE: true,            // Show debug information
        SHOW_HITBOXES: false,        // Render collision boxes
        SHOW_FPS: true,              // Display FPS counter
    },
    
    // ========================================
    // PLAYER CONFIGURATION
    // ========================================
    PLAYER: {
        // Base stats (these are starting values)
        BASE_HEALTH: 100,            // Starting health points (50-200 recommended)
        BASE_MANA: 50,               // Starting mana points (25-100 recommended)
        BASE_ATTACK: 15,             // Base attack damage (10-25 recommended)
        BASE_DEFENSE: 5,             // Base defense value (0-15 recommended)
        BASE_SPEED: 120,             // Movement speed in pixels/second (80-200 recommended)
        
        // Movement settings
        ACCELERATION: 800,           // How quickly player reaches max speed (400-1200)
        FRICTION: 0.85,              // Movement friction when not inputting (0.7-0.95)
        DIAGONAL_SPEED_FACTOR: 0.707, // Speed reduction for diagonal movement (√2/2)
        
        // Combat settings
        ATTACK_RANGE: 48,            // Attack reach in pixels (32-80 recommended)
        ATTACK_COOLDOWN: 500,        // Milliseconds between attacks (300-800)
        INVINCIBILITY_FRAMES: 1000,  // Milliseconds of invincibility after taking damage
        KNOCKBACK_FORCE: 150,        // Knockback strength when hit (100-300)
        
        // Leveling and progression
        STARTING_LEVEL: 1,           // Initial player level
        EXP_TO_NEXT_LEVEL: 100,      // Base experience needed for level 2
        EXP_SCALING_FACTOR: 1.5,     // Multiplier for each level (1.2-2.0 recommended)
        HEALTH_PER_LEVEL: 10,        // Health gained per level up
        MANA_PER_LEVEL: 5,           // Mana gained per level up
        ATTACK_PER_LEVEL: 2,         // Attack damage gained per level up
        DEFENSE_PER_LEVEL: 1,        // Defense gained per level up
        
        // Visual settings
        SPRITE_WIDTH: 16,            // Player sprite width
        SPRITE_HEIGHT: 16,           // Player sprite height
        HITBOX_WIDTH: 12,            // Collision box width (smaller than sprite)
        HITBOX_HEIGHT: 12,           // Collision box height
    },
    
    // ========================================
    // ENEMY CONFIGURATION
    // ========================================
    ENEMIES: {
        // Goblin - Basic melee enemy
        GOBLIN: {
            HEALTH: 30,              // Health points (20-50 recommended)
            ATTACK: 8,               // Attack damage (5-15 recommended)
            DEFENSE: 2,              // Defense value (0-5 recommended)
            SPEED: 80,               // Movement speed (60-120 recommended)
            EXP_REWARD: 15,          // Experience points given when killed
            
            // AI Behavior
            DETECTION_RANGE: 120,    // How far they can see player (80-200)
            ATTACK_RANGE: 20,        // How close they need to be to attack
            ATTACK_COOLDOWN: 1200,   // Milliseconds between attacks
            CHASE_DURATION: 3000,    // How long they chase after losing sight
            WANDER_SPEED: 40,        // Speed when wandering randomly
            WANDER_CHANGE_INTERVAL: 2000, // How often to change wander direction
            
            // Visual
            SPRITE_WIDTH: 16,
            SPRITE_HEIGHT: 16,
            HITBOX_WIDTH: 14,
            HITBOX_HEIGHT: 14,
        },
        
        // Skeleton - Ranged enemy
        SKELETON: {
            HEALTH: 25,
            ATTACK: 12,
            DEFENSE: 1,
            SPEED: 60,
            EXP_REWARD: 20,
            
            // AI Behavior
            DETECTION_RANGE: 150,
            ATTACK_RANGE: 100,       // Longer range for projectiles
            ATTACK_COOLDOWN: 2000,
            CHASE_DURATION: 4000,
            WANDER_SPEED: 30,
            WANDER_CHANGE_INTERVAL: 3000,
            RETREAT_DISTANCE: 60,    // How far to back away from player
            
            // Projectile settings
            PROJECTILE_SPEED: 150,
            PROJECTILE_LIFETIME: 2000,
            
            SPRITE_WIDTH: 16,
            SPRITE_HEIGHT: 16,
            HITBOX_WIDTH: 14,
            HITBOX_HEIGHT: 14,
        },
        
        // Orc - Tank enemy
        ORC: {
            HEALTH: 60,
            ATTACK: 20,
            DEFENSE: 8,
            SPEED: 50,
            EXP_REWARD: 35,
            
            // AI Behavior
            DETECTION_RANGE: 100,
            ATTACK_RANGE: 25,
            ATTACK_COOLDOWN: 1800,
            CHASE_DURATION: 5000,
            WANDER_SPEED: 25,
            WANDER_CHANGE_INTERVAL: 4000,
            CHARGE_SPEED: 120,       // Speed when charging at player
            CHARGE_DISTANCE: 80,     // Distance to trigger charge attack
            
            SPRITE_WIDTH: 16,
            SPRITE_HEIGHT: 16,
            HITBOX_WIDTH: 15,
            HITBOX_HEIGHT: 15,
        },

        // Troll - Regenerating tank
        TROLL: {
            HEALTH: 100,
            ATTACK: 25,
            DEFENSE: 10,
            SPEED: 40,
            EXP_REWARD: 75,

            // AI Behavior
            DETECTION_RANGE: 80,
            ATTACK_RANGE: 30,
            ATTACK_COOLDOWN: 2500,
            CHASE_DURATION: 8000,
            WANDER_SPEED: 20,
            WANDER_CHANGE_INTERVAL: 5000,

            // Special abilities
            REGENERATION_RATE: 2,        // HP per second
            REGENERATION_DELAY: 3000,    // Delay after taking damage

            SPRITE_WIDTH: 16,
            SPRITE_HEIGHT: 16,
            HITBOX_WIDTH: 15,
            HITBOX_HEIGHT: 15,
        },

        // Spider - Fast poisonous enemy
        SPIDER: {
            HEALTH: 18,
            ATTACK: 6,
            DEFENSE: 0,
            SPEED: 100,
            EXP_REWARD: 12,

            // AI Behavior
            DETECTION_RANGE: 140,
            ATTACK_RANGE: 18,
            ATTACK_COOLDOWN: 800,
            CHASE_DURATION: 2500,
            WANDER_SPEED: 60,
            WANDER_CHANGE_INTERVAL: 1000,
            RETREAT_DISTANCE: 40,

            // Poison settings
            POISON_DAMAGE: 2,
            POISON_DURATION: 5000,
            POISON_TICK_RATE: 1000,

            SPRITE_WIDTH: 16,
            SPRITE_HEIGHT: 16,
            HITBOX_WIDTH: 12,
            HITBOX_HEIGHT: 12,
        },

        // Wraith - Phasing enemy
        WRAITH: {
            HEALTH: 35,
            ATTACK: 18,
            DEFENSE: 2,
            SPEED: 70,
            EXP_REWARD: 50,

            // AI Behavior
            DETECTION_RANGE: 160,
            ATTACK_RANGE: 25,
            ATTACK_COOLDOWN: 1800,
            CHASE_DURATION: 6000,
            WANDER_SPEED: 35,
            WANDER_CHANGE_INTERVAL: 2500,

            // Phase ability
            PHASE_DURATION: 2000,
            PHASE_COOLDOWN: 8000,
            PHASE_SPEED_MULTIPLIER: 1.5,

            SPRITE_WIDTH: 16,
            SPRITE_HEIGHT: 16,
            HITBOX_WIDTH: 14,
            HITBOX_HEIGHT: 14,
        },

        // Minotaur - Boss enemy with charge attack
        MINOTAUR: {
            HEALTH: 150,
            ATTACK: 30,
            DEFENSE: 12,
            SPEED: 45,
            EXP_REWARD: 120,

            // AI Behavior
            DETECTION_RANGE: 120,
            ATTACK_RANGE: 28,
            ATTACK_COOLDOWN: 2200,
            CHASE_DURATION: 12000,
            WANDER_SPEED: 25,
            WANDER_CHANGE_INTERVAL: 6000,

            // Charge attack
            CHARGE_SPEED: 150,
            CHARGE_DISTANCE: 100,
            CHARGE_DAMAGE_MULTIPLIER: 2.0,
            CHARGE_COOLDOWN: 5000,

            SPRITE_WIDTH: 16,
            SPRITE_HEIGHT: 16,
            HITBOX_WIDTH: 15,
            HITBOX_HEIGHT: 15,
        },

        // Spawn rates (probability out of 100)
        SPAWN_RATES: {
            GOBLIN: 30,              // 30% chance
            SKELETON: 25,            // 25% chance
            ORC: 20,                 // 20% chance
            SPIDER: 15,              // 15% chance
            TROLL: 6,                // 6% chance
            WRAITH: 3,               // 3% chance
            MINOTAUR: 1              // 1% chance (boss)
        }
    },
    
    // ========================================
    // ITEM CONFIGURATION
    // ========================================
    ITEMS: {
        // Weapons
        WEAPONS: {
            WOODEN_SWORD: {
                NAME: "Wooden Sword",
                ATTACK_BONUS: 5,
                DEFENSE_BONUS: 0,
                RARITY: "COMMON",    // COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
                DROP_RATE: 30,       // Percentage chance to drop
            },
            IRON_SWORD: {
                NAME: "Iron Sword",
                ATTACK_BONUS: 12,
                DEFENSE_BONUS: 0,
                RARITY: "UNCOMMON",
                DROP_RATE: 15,
            },
            STEEL_SWORD: {
                NAME: "Steel Sword",
                ATTACK_BONUS: 20,
                DEFENSE_BONUS: 2,
                RARITY: "RARE",
                DROP_RATE: 5,
            },
            BATTLE_AXE: {
                NAME: "Battle Axe",
                ATTACK_BONUS: 25,
                DEFENSE_BONUS: 0,
                RARITY: "RARE",
                DROP_RATE: 4,
                SPECIAL_EFFECT: "cleave" // Hits multiple enemies
            },
            MAGIC_STAFF: {
                NAME: "Magic Staff",
                ATTACK_BONUS: 15,
                DEFENSE_BONUS: 0,
                RARITY: "UNCOMMON",
                DROP_RATE: 8,
                SPECIAL_EFFECT: "mana_boost" // Increases mana regeneration
            },
            DRAGON_BLADE: {
                NAME: "Dragon Blade",
                ATTACK_BONUS: 35,
                DEFENSE_BONUS: 5,
                RARITY: "LEGENDARY",
                DROP_RATE: 1,
                SPECIAL_EFFECT: "fire_damage" // Adds fire damage
            },
            SHADOW_DAGGER: {
                NAME: "Shadow Dagger",
                ATTACK_BONUS: 18,
                DEFENSE_BONUS: 0,
                RARITY: "EPIC",
                DROP_RATE: 2,
                SPECIAL_EFFECT: "critical_chance" // Higher critical hit chance
            },
        },
        
        // Armor
        ARMOR: {
            LEATHER_ARMOR: {
                NAME: "Leather Armor",
                ATTACK_BONUS: 0,
                DEFENSE_BONUS: 8,
                RARITY: "COMMON",
                DROP_RATE: 25,
            },
            CHAIN_MAIL: {
                NAME: "Chain Mail",
                ATTACK_BONUS: 0,
                DEFENSE_BONUS: 15,
                RARITY: "UNCOMMON",
                DROP_RATE: 12,
            },
            PLATE_ARMOR: {
                NAME: "Plate Armor",
                ATTACK_BONUS: 0,
                DEFENSE_BONUS: 25,
                RARITY: "RARE",
                DROP_RATE: 4,
            },
            MAGIC_ROBES: {
                NAME: "Magic Robes",
                ATTACK_BONUS: 5,
                DEFENSE_BONUS: 12,
                RARITY: "UNCOMMON",
                DROP_RATE: 10,
                SPECIAL_EFFECT: "mana_boost"
            },
            DRAGON_SCALE_ARMOR: {
                NAME: "Dragon Scale Armor",
                ATTACK_BONUS: 8,
                DEFENSE_BONUS: 35,
                RARITY: "LEGENDARY",
                DROP_RATE: 1,
                SPECIAL_EFFECT: "fire_resistance"
            },
            SHADOW_CLOAK: {
                NAME: "Shadow Cloak",
                ATTACK_BONUS: 3,
                DEFENSE_BONUS: 15,
                RARITY: "EPIC",
                DROP_RATE: 2,
                SPECIAL_EFFECT: "stealth" // Reduces enemy detection range
            },
        },
        
        // Consumables
        CONSUMABLES: {
            HEALTH_POTION: {
                NAME: "Health Potion",
                HEALTH_RESTORE: 30,  // Amount of health restored
                RARITY: "COMMON",
                DROP_RATE: 40,
            },
            MANA_POTION: {
                NAME: "Mana Potion", 
                MANA_RESTORE: 20,    // Amount of mana restored
                RARITY: "COMMON",
                DROP_RATE: 30,
            },
            GREATER_HEALTH_POTION: {
                NAME: "Greater Health Potion",
                HEALTH_RESTORE: 60,
                RARITY: "UNCOMMON",
                DROP_RATE: 15,
            },
            STRENGTH_POTION: {
                NAME: "Strength Potion",
                ATTACK_BOOST: 10,
                DURATION: 30000, // 30 seconds
                RARITY: "UNCOMMON",
                DROP_RATE: 12,
            },
            SPEED_POTION: {
                NAME: "Speed Potion",
                SPEED_BOOST: 50,
                DURATION: 20000, // 20 seconds
                RARITY: "UNCOMMON",
                DROP_RATE: 10,
            },
            ANTIDOTE: {
                NAME: "Antidote",
                EFFECT: "cure_poison",
                RARITY: "COMMON",
                DROP_RATE: 20,
            },
            SCROLL_OF_FIREBALL: {
                NAME: "Scroll of Fireball",
                DAMAGE: 40,
                AREA_EFFECT: true,
                RARITY: "RARE",
                DROP_RATE: 5,
            },
            ELIXIR_OF_LIFE: {
                NAME: "Elixir of Life",
                HEALTH_RESTORE: 100,
                MAX_HEALTH_BOOST: 20,
                RARITY: "LEGENDARY",
                DROP_RATE: 1,
            },
        },
        
        // Rarity colors for UI
        RARITY_COLORS: {
            COMMON: "#ffffff",       // White
            UNCOMMON: "#1eff00",     // Green
            RARE: "#0070dd",         // Blue
            EPIC: "#a335ee",         // Purple
            LEGENDARY: "#ff8000",    // Orange
        }
    },

    // ========================================
    // DUNGEON GENERATION CONFIGURATION
    // ========================================
    DUNGEON: {
        // Room generation settings
        MIN_ROOM_SIZE: 5,            // Minimum room width/height in tiles (4-8 recommended)
        MAX_ROOM_SIZE: 12,           // Maximum room width/height in tiles (10-20 recommended)
        MAX_ROOMS: 15,               // Maximum number of rooms per floor (10-25 recommended)
        ROOM_ATTEMPTS: 50,           // How many times to try placing rooms (30-100)

        // Corridor settings
        CORRIDOR_WIDTH: 1,           // Width of connecting corridors in tiles (1-3)
        CORRIDOR_WINDINESS: 0.3,     // How winding corridors are (0.0-1.0)

        // Floor size
        FLOOR_WIDTH: 80,             // Floor width in tiles (60-120 recommended)
        FLOOR_HEIGHT: 60,            // Floor height in tiles (40-80 recommended)

        // Enemy spawning
        ENEMIES_PER_ROOM: {
            MIN: 1,                  // Minimum enemies per room
            MAX: 4,                  // Maximum enemies per room
        },
        ENEMY_SPAWN_CHANCE: 0.8,     // Probability a room will have enemies (0.0-1.0)

        // Item spawning
        ITEMS_PER_ROOM: {
            MIN: 0,                  // Minimum items per room
            MAX: 2,                  // Maximum items per room
        },
        ITEM_SPAWN_CHANCE: 0.4,      // Probability a room will have items (0.0-1.0)

        // Special rooms
        TREASURE_ROOM_CHANCE: 0.1,   // Probability of treasure room (0.0-0.3)
        BOSS_ROOM_CHANCE: 0.05,      // Probability of boss room (0.0-0.2)

        // Dungeon features
        TRAP_CHANCE: 0.05,           // Probability of trap in corridor (0.0-0.2)
        TREASURE_CHEST_CHANCE: 0.08, // Probability of treasure chest in room (0.0-0.3)
        SECRET_WALL_CHANCE: 0.03,    // Probability of secret wall (0.0-0.1)
        LOCKED_DOOR_CHANCE: 0.15,    // Probability of locked door (0.0-0.5)

        // Trap settings
        SPIKE_TRAP_DAMAGE: 15,       // Damage from spike traps
        POISON_TRAP_DAMAGE: 8,       // Damage from poison traps
        POISON_DURATION: 5000,       // Poison effect duration (ms)

        // Treasure chest settings
        CHEST_ITEM_COUNT_MIN: 1,     // Minimum items in chest
        CHEST_ITEM_COUNT_MAX: 3,     // Maximum items in chest
        CHEST_RARE_ITEM_CHANCE: 0.3, // Chance for rare+ items in chest

        // Tile types for generation
        TILE_TYPES: {
            WALL: 0,
            FLOOR: 1,
            DOOR: 2,
            STAIRS_DOWN: 3,
            STAIRS_UP: 4,
            TRAP: 5,
            TREASURE_CHEST: 6,
            LOCKED_DOOR: 7,
            SECRET_WALL: 8,
            PRESSURE_PLATE: 9
        }
    },

    // ========================================
    // COMBAT SYSTEM CONFIGURATION
    // ========================================
    COMBAT: {
        // Damage calculation settings
        DAMAGE_VARIANCE: 0.2,        // Random damage variance (±20%) (0.0-0.5 recommended)
        CRITICAL_HIT_CHANCE: 0.1,    // Probability of critical hit (0.0-0.3)
        CRITICAL_HIT_MULTIPLIER: 2.0, // Damage multiplier for crits (1.5-3.0)

        // Defense calculation
        DEFENSE_REDUCTION_FACTOR: 0.5, // How much defense reduces damage (0.3-0.8)
        MAX_DEFENSE_REDUCTION: 0.8,   // Maximum damage reduction from defense (0.5-0.9)

        // Status effects duration (in milliseconds)
        POISON_DURATION: 5000,        // How long poison lasts
        POISON_DAMAGE_INTERVAL: 1000, // How often poison deals damage
        STUN_DURATION: 2000,          // How long stun lasts
        SLOW_DURATION: 3000,          // How long slow effect lasts
        SLOW_FACTOR: 0.5,             // Speed reduction when slowed (0.3-0.7)

        // Knockback settings
        KNOCKBACK_FRICTION: 0.9,      // How quickly knockback fades (0.8-0.95)
        MIN_KNOCKBACK_SPEED: 10,      // Minimum speed before knockback stops
    },

    // ========================================
    // USER INTERFACE CONFIGURATION
    // ========================================
    UI: {
        // Mobile responsiveness
        MOBILE_BREAKPOINT: 768,       // Screen width where mobile UI activates
        TOUCH_BUTTON_SIZE: 60,        // Size of touch control buttons
        TOUCH_DEADZONE: 10,           // Minimum touch movement to register

        // UI scaling for different screen sizes
        UI_SCALE_FACTORS: {
            SMALL: 0.8,               // For screens < 600px
            MEDIUM: 1.0,              // For screens 600-1200px
            LARGE: 1.2,               // For screens > 1200px
        },

        // HUD settings
        HEALTH_BAR_WIDTH: 200,        // Width of health bar in pixels
        STAT_BAR_HEIGHT: 20,          // Height of stat bars
        UI_FADE_SPEED: 0.05,          // How fast UI elements fade (0.01-0.1)

        // Inventory settings
        INVENTORY_SLOTS: 20,          // Number of inventory slots
        INVENTORY_ROWS: 4,            // Number of rows in inventory grid
        SLOT_SIZE: 40,                // Size of each inventory slot

        // Text and fonts
        FONT_SIZE_SMALL: 12,
        FONT_SIZE_MEDIUM: 16,
        FONT_SIZE_LARGE: 24,
        DAMAGE_TEXT_DURATION: 1500,   // How long damage numbers show
        DAMAGE_TEXT_RISE_SPEED: 30,   // How fast damage text rises
    },

    // ========================================
    // AUDIO CONFIGURATION
    // ========================================
    AUDIO: {
        // Volume settings (0.0 to 1.0)
        MASTER_VOLUME: 0.7,
        MUSIC_VOLUME: 0.5,
        SFX_VOLUME: 0.8,

        // Audio file paths
        SOUNDS: {
            PLAYER_ATTACK: "assets/sounds/player_attack.wav",
            PLAYER_HIT: "assets/sounds/player_hit.wav",
            ENEMY_HIT: "assets/sounds/enemy_hit.wav",
            ITEM_PICKUP: "assets/sounds/item_pickup.wav",
            LEVEL_UP: "assets/sounds/level_up.wav",
            DOOR_OPEN: "assets/sounds/door_open.wav",
        },

        MUSIC: {
            DUNGEON_THEME: "assets/sounds/dungeon_theme.mp3",
            COMBAT_THEME: "assets/sounds/combat_theme.mp3",
        }
    },

    // ========================================
    // INPUT CONFIGURATION
    // ========================================
    INPUT: {
        // Keyboard controls (key codes)
        KEYS: {
            MOVE_UP: ["KeyW", "ArrowUp"],
            MOVE_DOWN: ["KeyS", "ArrowDown"],
            MOVE_LEFT: ["KeyA", "ArrowLeft"],
            MOVE_RIGHT: ["KeyD", "ArrowRight"],
            ATTACK: ["Space"],
            INTERACT: ["KeyE"],
            INVENTORY: ["KeyI", "Tab"],
            PAUSE: ["Escape"],
        },

        // Mouse settings
        MOUSE_SENSITIVITY: 1.0,       // Mouse movement sensitivity (0.5-2.0)

        // Touch settings for mobile
        TOUCH_MOVE_SENSITIVITY: 1.5,  // Touch movement sensitivity (1.0-3.0)
        VIRTUAL_JOYSTICK_SIZE: 100,   // Size of virtual joystick
        VIRTUAL_JOYSTICK_DEADZONE: 0.2, // Deadzone for virtual joystick (0.1-0.4)
    },

    // ========================================
    // PERFORMANCE CONFIGURATION
    // ========================================
    PERFORMANCE: {
        // Rendering optimizations
        MAX_PARTICLES: 100,           // Maximum particle effects on screen
        PARTICLE_POOL_SIZE: 200,      // Pre-allocated particle pool size
        MAX_DAMAGE_TEXTS: 20,         // Maximum floating damage texts

        // Entity limits
        MAX_ENEMIES_ON_SCREEN: 15,    // Maximum enemies rendered at once
        MAX_ITEMS_ON_SCREEN: 30,      // Maximum items rendered at once

        // Update frequencies (lower = better performance)
        AI_UPDATE_FREQUENCY: 3,       // Update AI every N frames (1-5)
        PATHFINDING_UPDATE_FREQUENCY: 5, // Update pathfinding every N frames

        // Culling distances
        ENTITY_CULL_DISTANCE: 400,    // Don't update entities beyond this distance
        RENDER_CULL_DISTANCE: 500,    // Don't render entities beyond this distance
    }
};

// Freeze the config object to prevent accidental modifications
Object.freeze(CONFIG);

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
