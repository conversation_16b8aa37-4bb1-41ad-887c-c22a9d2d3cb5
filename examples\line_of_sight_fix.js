/**
 * EXAMPLE: Line of Sight Fix for Enemy AI
 * 
 * Replace the current canSeePlayer method in enemy.js with this implementation
 */

canSeePlayer(player) {
    if (!player || !window.game || !window.game.dungeon) return false;
    
    const startX = this.x + this.width / 2;
    const startY = this.y + this.height / 2;
    const endX = player.x + player.width / 2;
    const endY = player.y + player.height / 2;
    
    return this.raycastToTarget(startX, startY, endX, endY);
}

raycastToTarget(x1, y1, x2, y2) {
    const dungeon = window.game.dungeon;
    const tileSize = CONFIG.GAME.SCALED_TILE_SIZE;
    
    // Use Bresenham's line algorithm for raycasting
    const dx = Math.abs(x2 - x1);
    const dy = Math.abs(y2 - y1);
    const sx = x1 < x2 ? 1 : -1;
    const sy = y1 < y2 ? 1 : -1;
    let err = dx - dy;
    
    let currentX = x1;
    let currentY = y1;
    
    while (true) {
        // Convert world coordinates to tile coordinates
        const tileX = Math.floor(currentX / tileSize);
        const tileY = Math.floor(currentY / tileSize);
        
        // Check if current tile is a wall
        if (dungeon.getTile(tileX, tileY) === CONFIG.DUNGEON.TILE_TYPES.WALL) {
            return false; // Line of sight blocked
        }
        
        // Check if we've reached the target
        if (Math.abs(currentX - x2) < tileSize/2 && Math.abs(currentY - y2) < tileSize/2) {
            return true; // Clear line of sight
        }
        
        // Move to next point
        const e2 = 2 * err;
        if (e2 > -dy) {
            err -= dy;
            currentX += sx * (tileSize / Math.max(dx, dy));
        }
        if (e2 < dx) {
            err += dx;
            currentY += sy * (tileSize / Math.max(dx, dy));
        }
    }
}
