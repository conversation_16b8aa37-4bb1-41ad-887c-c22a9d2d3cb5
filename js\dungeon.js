/**
 * <PERSON>UN<PERSON>ON GENERATION SYSTEM
 * 
 * Generates procedural dungeons with rooms and corridors.
 */

class DungeonGenerator {
    constructor() {
        this.width = CONFIG.DUNGEON.FLOOR_WIDTH;
        this.height = CONFIG.DUNGEON.FLOOR_HEIGHT;
    }
    
    generate() {
        const dungeon = new Dungeon(this.width, this.height);

        // Generate rooms
        const rooms = this.generateRooms();
        dungeon.rooms = rooms;

        // Fill in the tile map first
        this.generateTileMap(dungeon, rooms);

        // Then generate corridors (this will carve paths through walls)
        this.generateCorridors(dungeon, rooms);

        return dungeon;
    }
    
    generateRooms() {
        const rooms = [];
        let attempts = 0;
        const maxAttempts = CONFIG.DUNGEON.ROOM_ATTEMPTS;

        while (rooms.length < CONFIG.DUNGEON.MAX_ROOMS && attempts < maxAttempts) {
            const room = this.createRandomRoom();

            if (this.isValidRoomPlacement(room, rooms)) {
                rooms.push(room);
                console.log(`Generated room ${rooms.length}: (${room.x}, ${room.y}) ${room.width}x${room.height}`);
            }
            attempts++;
        }

        console.log(`Generated ${rooms.length} rooms in ${attempts} attempts`);
        return rooms;
    }
    
    createRandomRoom() {
        const width = Utils.randomInt(CONFIG.DUNGEON.MIN_ROOM_SIZE, CONFIG.DUNGEON.MAX_ROOM_SIZE);
        const height = Utils.randomInt(CONFIG.DUNGEON.MIN_ROOM_SIZE, CONFIG.DUNGEON.MAX_ROOM_SIZE);
        const x = Utils.randomInt(1, this.width - width - 1);
        const y = Utils.randomInt(1, this.height - height - 1);
        
        return {
            x: x,
            y: y,
            width: width,
            height: height,
            centerX: x + Math.floor(width / 2),
            centerY: y + Math.floor(height / 2)
        };
    }
    
    isValidRoomPlacement(newRoom, existingRooms) {
        for (const room of existingRooms) {
            if (this.roomsOverlap(newRoom, room)) {
                return false;
            }
        }
        return true;
    }
    
    roomsOverlap(room1, room2) {
        return room1.x < room2.x + room2.width + 1 &&
               room1.x + room1.width + 1 > room2.x &&
               room1.y < room2.y + room2.height + 1 &&
               room1.y + room1.height + 1 > room2.y;
    }
    
    generateCorridors(dungeon, rooms) {
        // Simple corridor generation - connect each room to the next
        for (let i = 0; i < rooms.length - 1; i++) {
            this.createCorridor(dungeon, rooms[i], rooms[i + 1]);
        }
    }
    
    createCorridor(dungeon, room1, room2) {
        // Simple L-shaped corridor connecting room centers
        const x1 = room1.centerX;
        const y1 = room1.centerY;
        const x2 = room2.centerX;
        const y2 = room2.centerY;

        console.log(`Creating corridor from room (${x1}, ${y1}) to room (${x2}, ${y2})`);

        // Horizontal corridor first
        const startX = Math.min(x1, x2);
        const endX = Math.max(x1, x2);
        for (let x = startX; x <= endX; x++) {
            dungeon.setTile(x, y1, CONFIG.DUNGEON.TILE_TYPES.FLOOR);
            // Also clear adjacent tiles to make corridor wider if needed
            if (CONFIG.DUNGEON.CORRIDOR_WIDTH > 1) {
                dungeon.setTile(x, y1 - 1, CONFIG.DUNGEON.TILE_TYPES.FLOOR);
                dungeon.setTile(x, y1 + 1, CONFIG.DUNGEON.TILE_TYPES.FLOOR);
            }
        }

        // Vertical corridor second
        const startY = Math.min(y1, y2);
        const endY = Math.max(y1, y2);
        for (let y = startY; y <= endY; y++) {
            dungeon.setTile(x2, y, CONFIG.DUNGEON.TILE_TYPES.FLOOR);
            // Also clear adjacent tiles to make corridor wider if needed
            if (CONFIG.DUNGEON.CORRIDOR_WIDTH > 1) {
                dungeon.setTile(x2 - 1, y, CONFIG.DUNGEON.TILE_TYPES.FLOOR);
                dungeon.setTile(x2 + 1, y, CONFIG.DUNGEON.TILE_TYPES.FLOOR);
            }
        }
    }
    
    generateTileMap(dungeon, rooms) {
        // Initialize with walls
        for (let x = 0; x < this.width; x++) {
            for (let y = 0; y < this.height; y++) {
                dungeon.setTile(x, y, CONFIG.DUNGEON.TILE_TYPES.WALL);
            }
        }
        
        // Carve out rooms
        rooms.forEach(room => {
            for (let x = room.x; x < room.x + room.width; x++) {
                for (let y = room.y; y < room.y + room.height; y++) {
                    dungeon.setTile(x, y, CONFIG.DUNGEON.TILE_TYPES.FLOOR);
                }
            }
        });
    }
}

class Dungeon {
    constructor(width, height) {
        this.width = width;
        this.height = height;
        this.tiles = new Array(width * height).fill(CONFIG.DUNGEON.TILE_TYPES.WALL);
        this.rooms = [];
    }
    
    setTile(x, y, tileType) {
        if (x >= 0 && x < this.width && y >= 0 && y < this.height) {
            this.tiles[y * this.width + x] = tileType;
        }
    }
    
    getTile(x, y) {
        if (x >= 0 && x < this.width && y >= 0 && y < this.height) {
            return this.tiles[y * this.width + x];
        }
        return CONFIG.DUNGEON.TILE_TYPES.WALL;
    }
    
    render(renderer) {
        const camera = renderer.camera;
        const tileSize = CONFIG.GAME.SCALED_TILE_SIZE;
        
        // Calculate visible tile range
        const startX = Math.floor(camera.x / tileSize);
        const startY = Math.floor(camera.y / tileSize);
        const endX = Math.ceil((camera.x + renderer.canvas.width) / tileSize);
        const endY = Math.ceil((camera.y + renderer.canvas.height) / tileSize);
        
        for (let x = Math.max(0, startX); x < Math.min(this.width, endX); x++) {
            for (let y = Math.max(0, startY); y < Math.min(this.height, endY); y++) {
                const tileType = this.getTile(x, y);
                const worldX = x * tileSize;
                const worldY = y * tileSize;
                
                switch (tileType) {
                    case CONFIG.DUNGEON.TILE_TYPES.WALL:
                        renderer.drawSprite('wall', worldX, worldY);
                        break;
                    case CONFIG.DUNGEON.TILE_TYPES.FLOOR:
                        renderer.drawSprite('floor', worldX, worldY);
                        break;
                    case CONFIG.DUNGEON.TILE_TYPES.DOOR:
                        renderer.drawSprite('door', worldX, worldY);
                        break;
                }
            }
        }

        // Debug: Draw room outlines and corridors
        if (CONFIG.GAME.DEBUG_MODE) {
            this.drawDebugInfo(renderer);
        }
    }

    drawDebugInfo(renderer) {
        // Draw room outlines
        renderer.ctx.strokeStyle = '#00ff00';
        renderer.ctx.lineWidth = 2;

        this.rooms.forEach((room, index) => {
            const worldX = room.x * CONFIG.GAME.SCALED_TILE_SIZE;
            const worldY = room.y * CONFIG.GAME.SCALED_TILE_SIZE;
            const worldWidth = room.width * CONFIG.GAME.SCALED_TILE_SIZE;
            const worldHeight = room.height * CONFIG.GAME.SCALED_TILE_SIZE;

            renderer.ctx.strokeRect(
                worldX - renderer.camera.x,
                worldY - renderer.camera.y,
                worldWidth,
                worldHeight
            );

            // Draw room number
            renderer.drawWorldText(
                index.toString(),
                worldX + worldWidth / 2,
                worldY + worldHeight / 2,
                '#00ff00',
                16
            );
        });
    }
}

window.DungeonGenerator = DungeonGenerator;
window.Dungeon = Dungeon;
