/**
 * ASSET LOADER
 * 
 * Centralized asset loading system with promises, caching, and error handling.
 * Supports images, audio, and JSON manifests.
 */

class AssetLoader {
    constructor() {
        this.loadedAssets = new Map();
        this.loadingPromises = new Map();
        this.manifest = null;
        this.loadingProgress = 0;
        this.totalAssets = 0;
        this.loadedCount = 0;
    }
    
    async loadManifest(manifestPath = 'assets/manifest.json') {
        try {
            const response = await fetch(manifestPath);
            if (!response.ok) {
                throw new Error(`Failed to load manifest: ${response.status}`);
            }
            this.manifest = await response.json();
            console.log('Asset manifest loaded:', this.manifest);
        } catch (error) {
            console.warn('Failed to load asset manifest, using defaults:', error);
            this.manifest = this.getDefaultManifest();
        }
    }
    
    getDefaultManifest() {
        return {
            version: "1.0.0",
            sprites: {
                player: {
                    path: 'assets/sprites/player.png',
                    frames: 4,
                    frameWidth: 16,
                    frameHeight: 16,
                    animations: {
                        idle: { frames: [0], frameRate: 1 },
                        walk: { frames: [0, 1, 2, 3], frameRate: 8 },
                        attack: { frames: [0, 1, 2, 3], frameRate: 12 }
                    }
                },
                goblin: {
                    path: 'assets/sprites/goblin.png',
                    frames: 4,
                    frameWidth: 16,
                    frameHeight: 16,
                    animations: {
                        idle: { frames: [0], frameRate: 1 },
                        walk: { frames: [0, 1, 2, 3], frameRate: 6 },
                        attack: { frames: [0, 1, 2, 3], frameRate: 10 }
                    }
                },
                skeleton: {
                    path: 'assets/sprites/skeleton.png',
                    frames: 4,
                    frameWidth: 16,
                    frameHeight: 16,
                    animations: {
                        idle: { frames: [0], frameRate: 1 },
                        walk: { frames: [0, 1, 2, 3], frameRate: 5 },
                        attack: { frames: [0, 1, 2, 3], frameRate: 8 }
                    }
                },
                orc: {
                    path: 'assets/sprites/orc.png',
                    frames: 4,
                    frameWidth: 16,
                    frameHeight: 16,
                    animations: {
                        idle: { frames: [0], frameRate: 1 },
                        walk: { frames: [0, 1, 2, 3], frameRate: 4 },
                        attack: { frames: [0, 1, 2, 3], frameRate: 6 }
                    }
                },
                wall: { path: 'assets/tiles/wall.png', frames: 1, frameWidth: 16, frameHeight: 16 },
                floor: { path: 'assets/tiles/floor.png', frames: 1, frameWidth: 16, frameHeight: 16 },
                door: { path: 'assets/tiles/door.png', frames: 1, frameWidth: 16, frameHeight: 16 },
                sword: { path: 'assets/items/sword.png', frames: 1, frameWidth: 16, frameHeight: 16 },
                armor: { path: 'assets/items/armor.png', frames: 1, frameWidth: 16, frameHeight: 16 },
                potion: { path: 'assets/items/potion.png', frames: 1, frameWidth: 16, frameHeight: 16 }
            },
            sounds: {
                player_attack: 'assets/sounds/player_attack.wav',
                player_hit: 'assets/sounds/player_hit.wav',
                enemy_hit: 'assets/sounds/enemy_hit.wav',
                item_pickup: 'assets/sounds/item_pickup.wav',
                level_up: 'assets/sounds/level_up.wav'
            },
            preload: [
                'player', 'goblin', 'skeleton', 'orc',
                'wall', 'floor', 'door',
                'sword', 'armor', 'potion',
                'player_attack', 'player_hit', 'item_pickup', 'level_up'
            ]
        };
    }
    
    async loadImage(path) {
        if (this.loadedAssets.has(path)) {
            return this.loadedAssets.get(path);
        }
        
        if (this.loadingPromises.has(path)) {
            return this.loadingPromises.get(path);
        }
        
        const promise = new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.loadedAssets.set(path, img);
                this.loadingPromises.delete(path);
                this.loadedCount++;
                this.updateProgress();
                resolve(img);
            };
            img.onerror = () => {
                this.loadingPromises.delete(path);
                console.warn(`Failed to load image: ${path}, using fallback`);
                const fallback = this.createFallbackSprite();
                this.loadedAssets.set(path, fallback);
                this.loadedCount++;
                this.updateProgress();
                resolve(fallback);
            };
            img.src = path;
        });
        
        this.loadingPromises.set(path, promise);
        return promise;
    }
    
    async loadAudio(path) {
        if (this.loadedAssets.has(path)) {
            return this.loadedAssets.get(path);
        }
        
        if (this.loadingPromises.has(path)) {
            return this.loadingPromises.get(path);
        }
        
        const promise = new Promise((resolve) => {
            const audio = new Audio();
            audio.oncanplaythrough = () => {
                this.loadedAssets.set(path, audio);
                this.loadingPromises.delete(path);
                this.loadedCount++;
                this.updateProgress();
                resolve(audio);
            };
            audio.onerror = () => {
                this.loadingPromises.delete(path);
                console.warn(`Failed to load audio: ${path}`);
                this.loadedCount++;
                this.updateProgress();
                resolve(null);
            };
            audio.src = path;
        });
        
        this.loadingPromises.set(path, promise);
        return promise;
    }
    
    createFallbackSprite() {
        const canvas = document.createElement('canvas');
        canvas.width = CONFIG.GAME.TILE_SIZE;
        canvas.height = CONFIG.GAME.TILE_SIZE;
        const ctx = canvas.getContext('2d');
        
        // Create a distinctive fallback pattern
        ctx.fillStyle = '#ff00ff'; // Magenta for missing textures
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#000000';
        ctx.fillRect(2, 2, canvas.width - 4, canvas.height - 4);
        ctx.fillStyle = '#ff00ff';
        ctx.fillRect(4, 4, canvas.width - 8, canvas.height - 8);
        
        return canvas;
    }
    
    async loadAllAssets() {
        if (!this.manifest) {
            await this.loadManifest();
        }
        
        // Count total assets
        this.totalAssets = Object.keys(this.manifest.sprites).length + Object.keys(this.manifest.sounds).length;
        this.loadedCount = 0;
        this.loadingProgress = 0;
        
        const loadPromises = [];
        
        // Load all sprites
        for (const [name, config] of Object.entries(this.manifest.sprites)) {
            loadPromises.push(this.loadImage(config.path));
        }
        
        // Load all sounds
        for (const [name, path] of Object.entries(this.manifest.sounds)) {
            loadPromises.push(this.loadAudio(path));
        }
        
        await Promise.all(loadPromises);
        console.log('All assets loaded successfully');
        return true;
    }
    
    updateProgress() {
        this.loadingProgress = this.totalAssets > 0 ? (this.loadedCount / this.totalAssets) : 1;
        
        // Dispatch progress event
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('assetLoadProgress', {
                detail: { progress: this.loadingProgress, loaded: this.loadedCount, total: this.totalAssets }
            }));
        }
    }
    
    getSprite(name) {
        const config = this.manifest?.sprites[name];
        if (!config) {
            console.warn(`Unknown sprite: ${name}`);
            return this.createFallbackSprite();
        }
        
        return this.loadedAssets.get(config.path) || this.createFallbackSprite();
    }
    
    getSpriteConfig(name) {
        return this.manifest?.sprites[name] || null;
    }
    
    getAudio(name) {
        const path = this.manifest?.sounds[name];
        if (!path) {
            console.warn(`Unknown audio: ${name}`);
            return null;
        }
        
        return this.loadedAssets.get(path);
    }
    
    isLoaded(assetPath) {
        return this.loadedAssets.has(assetPath);
    }
    
    getLoadingProgress() {
        return this.loadingProgress;
    }
    
    // Preload specific assets
    async preloadAssets(assetNames) {
        const promises = [];
        
        for (const name of assetNames) {
            const spriteConfig = this.manifest?.sprites[name];
            if (spriteConfig) {
                promises.push(this.loadImage(spriteConfig.path));
            }
            
            const soundPath = this.manifest?.sounds[name];
            if (soundPath) {
                promises.push(this.loadAudio(soundPath));
            }
        }
        
        await Promise.all(promises);
    }
    
    // Get animation data for a sprite
    getAnimationData(spriteName) {
        const spriteConfig = this.manifest?.sprites[spriteName];
        return spriteConfig?.animations || null;
    }

    // Get preload list from manifest
    getPreloadList() {
        return this.manifest?.preload || [];
    }

    // Load only preload assets
    async loadPreloadAssets() {
        const preloadList = this.getPreloadList();
        if (preloadList.length === 0) {
            console.log('No preload assets specified');
            return;
        }

        this.totalAssets = preloadList.length;
        this.loadedCount = 0;
        this.loadingProgress = 0;

        const promises = [];

        for (const assetName of preloadList) {
            // Check if it's a sprite
            const spriteConfig = this.manifest?.sprites[assetName];
            if (spriteConfig) {
                promises.push(this.loadImage(spriteConfig.path));
                continue;
            }

            // Check if it's a sound
            const soundPath = this.manifest?.sounds[assetName];
            if (soundPath) {
                promises.push(this.loadAudio(soundPath));
                continue;
            }

            console.warn(`Preload asset '${assetName}' not found in manifest`);
        }

        await Promise.all(promises);
        console.log('Preload assets loaded successfully');
    }

    // Get manifest version
    getVersion() {
        return this.manifest?.version || '1.0.0';
    }

    // Validate manifest structure
    validateManifest() {
        if (!this.manifest) {
            console.warn('No manifest loaded');
            return false;
        }

        const required = ['sprites', 'sounds'];
        for (const field of required) {
            if (!this.manifest[field]) {
                console.warn(`Manifest missing required field: ${field}`);
                return false;
            }
        }

        return true;
    }

    // Clear cache (useful for memory management)
    clearCache() {
        this.loadedAssets.clear();
        this.loadingPromises.clear();
        this.loadedCount = 0;
        this.loadingProgress = 0;
    }
}

// Export for use in other modules
window.AssetLoader = AssetLoader;
