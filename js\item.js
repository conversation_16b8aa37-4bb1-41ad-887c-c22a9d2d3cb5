/**
 * ITEM SYSTEM
 * 
 * Handles items, inventory, and item interactions.
 */

class Item extends Entity {
    constructor(x, y, itemType, itemCategory) {
        super(x, y, CONFIG.GAME.SCALED_TILE_SIZE, CONFIG.GAME.SCALED_TILE_SIZE);
        
        this.itemType = itemType;
        this.itemCategory = itemCategory;
        this.config = CONFIG.ITEMS[itemCategory.toUpperCase()][itemType.toUpperCase()];
        
        if (!this.config) {
            console.warn(`Unknown item: ${itemCategory}.${itemType}`);
            this.config = { NAME: "Unknown Item", RARITY: "COMMON" };
        }
        
        // Item properties
        this.name = this.config.NAME;
        this.rarity = this.config.RARITY;
        this.attackBonus = this.config.ATTACK_BONUS || 0;
        this.defenseBonus = this.config.DEFENSE_BONUS || 0;
        this.healthRestore = this.config.HEALTH_RESTORE || 0;
        this.manaRestore = this.config.MANA_RESTORE || 0;
        
        // Visual
        this.sprite = this.getItemSprite();
        this.type = 'item';
        
        // Make items non-collidable for movement
        this.solid = false;
    }
    
    getItemSprite() {
        // Map item types to sprites
        const spriteMap = {
            'weapons': 'sword',
            'armor': 'armor',
            'consumables': 'potion'
        };
        
        return spriteMap[this.itemCategory] || 'sword';
    }
    
    render(renderer) {
        super.render(renderer);
        
        // Add rarity glow effect
        const glowColor = Utils.getRarityColor(this.rarity);
        renderer.drawCircle(
            this.x + this.width / 2,
            this.y + this.height / 2,
            this.width / 2 + 2,
            glowColor + '40' // Add transparency
        );
    }
    
    onPickup(player) {
        console.log(`Picked up ${this.name} (${this.rarity})`);

        // Play pickup sound effect
        if (window.soundManager) {
            window.soundManager.playItemPickup();
        }

        if (this.itemCategory === 'consumables') {
            this.useConsumable(player);
        } else {
            // Add to inventory (simplified - just apply bonuses immediately)
            this.applyItemBonuses(player);
            player.inventory[this.itemCategory]++;
        }

        // Show pickup notification
        if (window.game && window.game.renderer) {
            window.game.renderer.addDamageText(
                player.x + player.width / 2,
                player.y - 20,
                `+${this.name}`,
                false
            );
        }

        // Add sparkle effect
        if (window.particlePool) {
            window.particlePool.createSparkles(
                this.x + this.width / 2,
                this.y + this.height / 2,
                6
            );
        }

        // Remove item from world
        this.alive = false;
    }
    
    useConsumable(player) {
        if (this.healthRestore > 0) {
            player.heal(this.healthRestore);
        }
        
        if (this.manaRestore > 0) {
            player.mana = Math.min(player.mana + this.manaRestore, player.maxMana);
        }
    }
    
    applyItemBonuses(player) {
        player.attack += this.attackBonus;
        player.defense += this.defenseBonus;
    }
}

window.Item = Item;
