<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Dungeon Crawler - .io Game</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #1a1a1a;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            overflow: hidden;
            touch-action: none;
        }
        
        #gameContainer {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100vw;
            height: 100vh;
            background: #000;
        }
        
        #gameCanvas {
            border: 2px solid #333;
            background: #222;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            pointer-events: none;
        }
        
        .stat-bar {
            width: 200px;
            height: 20px;
            background: #333;
            border: 2px solid #666;
            margin-bottom: 5px;
            position: relative;
        }
        
        .stat-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .health-fill { background: #e74c3c; }
        .mana-fill { background: #3498db; }
        .exp-fill { background: #f39c12; }
        
        .stat-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: bold;
            text-shadow: 1px 1px 1px #000;
        }
        
        #mobileControls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            display: none;
        }

        .control-button {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            margin: 5px;
            display: inline-block;
            text-align: center;
            line-height: 56px;
            font-size: 20px;
            user-select: none;
            touch-action: manipulation;
            transition: all 0.1s ease;
        }

        .control-button:active {
            background: rgba(255, 255, 255, 0.5);
            border-color: rgba(255, 255, 255, 0.8);
            transform: scale(0.95);
        }

        #gameInstructions {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            display: none;
            z-index: 200;
        }

        @media (max-width: 768px) {
            #mobileControls {
                display: block;
            }

            #gameInstructions {
                display: block;
            }
        }

        /* Game State Screens */
        .game-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .screen-content {
            text-align: center;
            color: white;
            max-width: 400px;
            padding: 40px;
            background: rgba(20, 20, 20, 0.95);
            border-radius: 10px;
            border: 2px solid #4a90e2;
        }

        .screen-content h1 {
            font-size: 3em;
            margin-bottom: 30px;
            color: #4a90e2;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .screen-content h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
            color: #4a90e2;
        }

        .menu-buttons {
            margin: 30px 0;
        }

        .menu-button {
            display: block;
            width: 100%;
            margin: 10px 0;
            padding: 15px 30px;
            font-size: 18px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .menu-button:hover {
            background: #357abd;
        }

        .menu-hint {
            margin-top: 20px;
            color: #aaa;
            font-style: italic;
        }

        /* Loading Screen */
        .loading-bar {
            width: 300px;
            height: 20px;
            background: #333;
            border-radius: 10px;
            margin: 20px auto;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #4a90e2, #6bb6ff);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Final Stats */
        #final-stats {
            margin: 20px 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
        }

        #final-stats p {
            margin: 10px 0;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <div class="stat-bar">
                <div class="stat-fill health-fill" id="healthBar"></div>
                <div class="stat-text" id="healthText">100/100</div>
            </div>
            <div class="stat-bar">
                <div class="stat-fill mana-fill" id="manaBar"></div>
                <div class="stat-text" id="manaText">50/50</div>
            </div>
            <div class="stat-bar">
                <div class="stat-fill exp-fill" id="expBar"></div>
                <div class="stat-text" id="expText">0/100</div>
            </div>
        </div>
        
        <div id="mobileControls">
            <div class="control-button" id="attackBtn">⚔</div>
        </div>

        <div id="gameInstructions">
            <h3>Controls</h3>
            <p><strong>Desktop:</strong> WASD to move, Mouse to aim, Click/Space to attack</p>
            <p><strong>Mobile:</strong> Touch left side to move, Touch right side to attack</p>
            <p>Collect items, defeat enemies, level up!</p>
            <button onclick="document.getElementById('gameInstructions').style.display='none'">Got it!</button>
        </div>

        <!-- Loading Screen -->
        <div id="loading-screen" class="game-screen">
            <div class="screen-content">
                <h1>Dungeon Crawler</h1>
                <div class="loading-bar">
                    <div id="loading-progress" class="loading-progress"></div>
                </div>
                <p>Loading assets...</p>
            </div>
        </div>

        <!-- Main Menu -->
        <div id="main-menu" class="game-screen" style="display: none;">
            <div class="screen-content">
                <h1>Dungeon Crawler</h1>
                <div class="menu-buttons">
                    <button id="start-game-btn" class="menu-button">Start Game</button>
                    <button id="settings-btn" class="menu-button">Settings</button>
                </div>
                <p class="menu-hint">Press ENTER to start</p>
            </div>
        </div>

        <!-- Pause Menu -->
        <div id="pause-menu" class="game-screen" style="display: none;">
            <div class="screen-content">
                <h2>Game Paused</h2>
                <div class="menu-buttons">
                    <button id="resume-btn" class="menu-button">Resume</button>
                    <button id="restart-btn" class="menu-button">Restart</button>
                    <button id="main-menu-btn" class="menu-button">Main Menu</button>
                </div>
                <p class="menu-hint">Press ESC to resume</p>
            </div>
        </div>

        <!-- Game Over Screen -->
        <div id="game-over-screen" class="game-screen" style="display: none;">
            <div class="screen-content">
                <h2>Game Over</h2>
                <div id="final-stats">
                    <p>Level: <span id="final-level">1</span></p>
                    <p>Score: <span id="final-score">0</span></p>
                </div>
                <div class="menu-buttons">
                    <button id="play-again-btn" class="menu-button">Play Again</button>
                    <button id="back-to-menu-btn" class="menu-button">Main Menu</button>
                </div>
                <p class="menu-hint">Press ENTER to play again</p>
            </div>
        </div>
    </div>

    <!-- Core game files -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/object_pool.js"></script>
    <script src="js/spatial_grid.js"></script>
    <script src="js/sound_manager.js"></script>
    <script src="js/game_state_manager.js"></script>
    <script src="js/asset_loader.js"></script>
    <script src="js/texture_atlas.js"></script>
    <script src="js/sprite_animation.js"></script>
    <script src="js/input.js"></script>
    <script src="js/renderer.js"></script>
    <script src="js/entity.js"></script>
    <script src="js/player.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/item.js"></script>
    <script src="js/dungeon.js"></script>
    <script src="js/combat.js"></script>
    <script src="js/dungeonFeatures.js"></script>

    <script src="js/main.js"></script>
</body>
</html>
